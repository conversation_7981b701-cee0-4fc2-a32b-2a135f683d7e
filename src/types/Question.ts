export interface Question {
  id: string;
  title: string;
  description: string;
  difficulty: 'easy' | 'medium' | 'hard';
  category: 'system-design' | 'architecture' | 'scalability' | 'database' | 'frontend' | 'backend';
  tags: string[];
  requirements: string[];  // Legacy field - will contain all requirements for backward compatibility
  functional_requirements?: string[];  // New field for FR only
  non_functional_requirements?: string[];  // New field for NFR only
  constraints?: string[];  // Technical implementation constraints only
  hints?: string[];
  sampleSolution?: string;
  createdAt: string;
  updatedAt: string;
}
