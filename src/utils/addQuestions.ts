import { supabase } from '@/lib/supabase';

interface QuestionData {
  id: string;
  title: string;
  description: string;
  difficulty: string;
  category: string;
  tags: string[];
  requirements: string[];
  constraints: string[];
  hints?: string[];
  sample_solution?: string;
}

// Medium System Design Questions from your file
const mediumQuestions: QuestionData[] = [
  {
    id: "11",
    title: "Twitter Feed",
    description: "Design a system to display a user's feed of tweets from followed accounts, including real-time updates and engagement (likes, retweets).",
    difficulty: "medium",
    category: "system-design",
    tags: ["twitter", "feed", "real-time", "social-media"],
    requirements: [
      "Publish tweets with text, images, or videos",
      "Follow/unfollow users",
      "Display a chronological or ranked feed",
      "Add likes, retweets, and replies"
    ],
    constraints: [
      "Load feed in < 500ms",
      "Handle 100k+ tweets per second",
      "99.95% availability",
      "Fan-out writes: Push tweets to all followers' timelines",
      "Caching: Use Redis for hot data (e.g., trending tweets)",
      "Load balancing: Distribute traffic across multiple servers",
      "Data denormalization: Store redundant data for faster reads"
    ]
  },
  {
    id: "12",
    title: "Instagram Feed",
    description: "Build a system to display a scrollable feed of images/videos with captions, comments, and likes.",
    difficulty: "medium",
    category: "system-design",
    tags: ["instagram", "feed", "images", "videos", "social-media"],
    requirements: [
      "Upload images/videos with captions",
      "Follow/unfollow users",
      "Display a ranked feed (e.g., by engagement)",
      "Add comments and likes"
    ],
    constraints: [
      "Load feed in < 1s",
      "Handle 1M+ uploads daily",
      "99.9% durability (no data loss)",
      "Image compression: Use JPEG/WebP for efficient storage",
      "CDN integration: Cache images globally",
      "Consistency: Eventual consistency for likes/comments",
      "Search: Use Elasticsearch for hashtag-based queries"
    ]
  },
  {
    id: "13",
    title: "WhatsApp",
    description: "Design a real-time messaging app with end-to-end encryption, presence detection, and group chats.",
    difficulty: "medium",
    category: "system-design",
    tags: ["messaging", "real-time", "encryption", "chat"],
    requirements: [
      "1:1 and group messaging",
      "End-to-end encryption (e.g., Signal Protocol)",
      "Online/offline status updates",
      "File sharing (images, videos, documents)"
    ],
    constraints: [
      "Message delivery in < 1s",
      "Support 100M+ daily active users",
      "99.99% message delivery success rate",
      "E2E encryption: Use asymmetric encryption (e.g., RSA) for key exchange",
      "Presence detection: Use WebSocket heartbeats",
      "Media optimization: Compress files before upload",
      "Store-and-forward: Temporarily store messages for offline users"
    ]
  },
  {
    id: "14",
    title: "Airbnb",
    description: "Create a platform for booking accommodations, including search, reviews, and payment processing.",
    difficulty: "medium",
    category: "system-design",
    tags: ["booking", "search", "payments", "reviews", "marketplace"],
    requirements: [
      "List properties with descriptions/photos",
      "Search/filter by location, dates, price",
      "Book and pay for stays",
      "Leave reviews after checkout"
    ],
    constraints: [
      "Search latency < 200ms",
      "Handle 1M+ bookings per day",
      "99.9% transaction integrity",
      "Search ranking: Use relevance score (e.g., location, reviews)",
      "Fraud detection: Flag suspicious bookings in real-time",
      "Payment processing: Integrate with Stripe/PayPal",
      "Calendar management: Track availability in a distributed cache"
    ]
  },
  {
    id: "15",
    title: "Spotify Playlist",
    description: "Design a system for collaborative playlists, streaming, and recommendations.",
    difficulty: "medium",
    category: "system-design",
    tags: ["music", "streaming", "playlists", "recommendations"],
    requirements: [
      "Create/edit playlists (public/private)",
      "Add/remove songs",
      "Stream music with low latency",
      "Generate recommendations (e.g., 'Discover Weekly')"
    ],
    constraints: [
      "Stream latency < 500ms",
      "Support 100M+ monthly active users",
      "99.95% uptime",
      "Audio streaming: Use HTTP Live Streaming (HLS) or DASH",
      "Collaborative playlists: Use CRDTs (Conflict-Free Replicated Data Types)",
      "Recommendations: Train ML models on user listening history",
      "Licensing: Track playback for royalty calculations"
    ]
  },
  {
    id: "16",
    title: "Uber ETA",
    description: "Calculate real-time estimated arrival times (ETAs) for rides, incorporating traffic and driver locations.",
    difficulty: "medium",
    category: "system-design",
    tags: ["uber", "eta", "geolocation", "real-time"],
    requirements: [
      "Track driver GPS coordinates",
      "Estimate arrival time for riders",
      "Update ETA dynamically during the trip"
    ],
    constraints: [
      "ETA accuracy within 10%",
      "Handle 10M+ ETA requests per minute",
      "Sub-second update latency",
      "Geospatial indexing: Use Elasticsearch or PostgreSQL with PostGIS",
      "Traffic data: Integrate with Google Maps API or HERE Technologies",
      "Kalman filter: Smooth GPS noise for accurate positioning",
      "Edge computing: Process location data locally to reduce latency"
    ]
  },
  {
    id: "17",
    title: "Amazon Shopping Cart",
    description: "Design a distributed shopping cart system with consistent item availability and pricing.",
    difficulty: "medium",
    category: "system-design",
    tags: ["e-commerce", "shopping-cart", "inventory", "distributed-systems"],
    requirements: [
      "Add/remove items",
      "Update cart across devices",
      "Reserve inventory during checkout",
      "Handle cart abandonment"
    ],
    constraints: [
      "99.9% cart consistency",
      "Handle 100k+ carts per second",
      "Low latency (< 200ms)",
      "Distributed sessions: Use sticky sessions or Redis for cart data",
      "Inventory reservation: Use compensating transactions (e.g., Saga pattern)",
      "Price consistency: Cache prices with short TTLs",
      "A/B testing: Dynamically show different cart UIs"
    ]
  },
  {
    id: "18",
    title: "Google Calendar",
    description: "Build a calendar system for scheduling events, reminders, and sharing.",
    difficulty: "medium",
    category: "system-design",
    tags: ["calendar", "scheduling", "events", "notifications"],
    requirements: [
      "Create/edit recurring events",
      "Invite attendees with RSVP",
      "Send reminders (email, push notifications)",
      "Share calendars (public/private)"
    ],
    constraints: [
      "Sync latency < 1s",
      "Support 1B+ events per day",
      "99.99% uptime",
      "Event storage: Use a relational database (e.g., Google Bigtable)",
      "Reminders: Use a pub/sub system (e.g., Kafka) for notifications",
      "Free/busy detection: Cache availability for faster scheduling",
      "Time zones: Normalize all timestamps to UTC"
    ]
  },
  {
    id: "19",
    title: "Twitter Direct Messages",
    description: "Design a DM system with offline messaging, typing indicators, and read receipts.",
    difficulty: "medium",
    category: "system-design",
    tags: ["messaging", "direct-messages", "real-time", "twitter"],
    requirements: [
      "Send/receive messages",
      "Display typing indicators",
      "Track read receipts",
      "Store message history"
    ],
    constraints: [
      "Delivery latency < 500ms",
      "Handle 1M+ messages per second",
      "99.95% message persistence",
      "Offline storage: Use a message queue (e.g., RabbitMQ) for delayed delivery",
      "Typing indicators: Use WebSocket heartbeats",
      "Read receipts: Update a distributed cache (e.g., Memcached)",
      "Encryption: Use TLS for data in transit"
    ]
  },
  {
    id: "20",
    title: "Instagram Stories",
    description: "Create an ephemeral content system for stories, polls, and analytics.",
    difficulty: "medium",
    category: "system-design",
    tags: ["instagram", "stories", "ephemeral", "analytics"],
    requirements: [
      "Post stories (images/videos) that expire in 24h",
      "Add interactive elements (e.g., polls, quizzes)",
      "Track views and engagement",
      "Allow story highlights"
    ],
    constraints: [
      "Story upload latency < 2s",
      "Handle 100M+ story views per hour",
      "99.9% accuracy in analytics",
      "Ephemerality: Use TTL (time-to-live) in the database",
      "Polls: Use a distributed cache (e.g., Redis) for real-time results",
      "View tracking: Sample views to reduce write load",
      "Augmented reality: Integrate with Spark AR for filters"
    ]
  }
];

// Additional medium questions (batch 2)
const mediumQuestionsBatch2: QuestionData[] = [
  {
    id: "21",
    title: "Instagram Ads",
    description: "Design a system for targeted ads on Instagram, including real-time bidding, click tracking, and performance analytics.",
    difficulty: "medium",
    category: "system-design",
    tags: ["ads", "targeting", "real-time-bidding", "analytics"],
    requirements: [
      "Deliver ads based on user demographics/interests",
      "Track clicks, impressions, and conversions",
      "Support real-time bidding (RTB) for ad slots",
      "Optimize ad rankings (e.g., cost per click)"
    ],
    constraints: [
      "Ad latency < 200ms",
      "Handle 1M+ ad requests per second",
      "99.9% accuracy in click tracking",
      "Ad targeting: Use user profiles and ML models",
      "RTB integration: Connect to demand-side platforms (DSPs)",
      "Fraud detection: Filter invalid clicks in real-time",
      "Cache creatives: Store ad media in CDNs (e.g., Cloudflare)"
    ]
  },
  {
    id: "22",
    title: "Uber Eats",
    description: "Build a food delivery platform for ordering, restaurant coordination, and real-time tracking.",
    difficulty: "medium",
    category: "system-design",
    tags: ["food-delivery", "ordering", "tracking", "marketplace"],
    requirements: [
      "Browse restaurants and menus",
      "Place orders with delivery estimates",
      "Track order status (preparing, en route)",
      "Handle cancellations/refunds"
    ],
    constraints: [
      "Order processing latency < 1s",
      "Support 100k+ concurrent orders",
      "99.95% accurate delivery ETAs",
      "Restaurant integration: Use APIs for menu updates",
      "Delivery routing: Optimize routes with Google Maps API",
      "Peak load handling: Use auto-scaling groups",
      "Compensation: Manage refunds via a transactional system"
    ]
  }
];

/**
 * Add questions to the database
 */
export const insertMediumQuestions = async (): Promise<{ success: boolean; error?: string; inserted: number }> => {
  try {
    let insertedCount = 0;

    // Insert batch 1
    for (const question of mediumQuestions) {
      const { error } = await supabase
        .from('questions')
        .insert([question]);

      if (error) {
        console.error(`Error inserting question ${question.id}:`, error);
        return { success: false, error: `Failed to insert question ${question.id}: ${error.message}`, inserted: insertedCount };
      }

      insertedCount++;
      console.log(`✅ Inserted question ${question.id}: ${question.title}`);
    }

    // Insert batch 2
    for (const question of mediumQuestionsBatch2) {
      const { error } = await supabase
        .from('questions')
        .insert([question]);

      if (error) {
        console.error(`Error inserting question ${question.id}:`, error);
        return { success: false, error: `Failed to insert question ${question.id}: ${error.message}`, inserted: insertedCount };
      }

      insertedCount++;
      console.log(`✅ Inserted question ${question.id}: ${question.title}`);
    }

    return { success: true, inserted: insertedCount };
  } catch (error) {
    console.error('Error inserting questions:', error);
    return { success: false, error: 'Failed to insert questions', inserted: 0 };
  }
};
