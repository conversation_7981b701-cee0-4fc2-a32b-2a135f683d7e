import { QuestionService } from '@/services/questionService';

/**
 * Test the updated QuestionService to verify FR/NFR columns are being fetched
 */
export const testQuestionServiceFRNFR = async () => {
  console.log('🧪 Testing QuestionService FR/NFR column fetching...');
  
  try {
    // Test 1: Load all questions and check for FR/NFR data
    console.log('📋 Loading all questions...');
    const questions = await QuestionService.loadQuestions();
    
    console.log(`✅ Loaded ${questions.length} questions`);
    
    // Check questions 26-30 specifically (the ones we just updated)
    const testQuestionIds = ['26', '27', '28', '29', '30'];
    const testQuestions = questions.filter(q => testQuestionIds.includes(q.id));
    
    console.log(`🔍 Found ${testQuestions.length} test questions (26-30)`);
    
    testQuestions.forEach(q => {
      const hasFR = q.functional_requirements && q.functional_requirements.length > 0;
      const hasNFR = q.non_functional_requirements && q.non_functional_requirements.length > 0;
      const status = hasFR && hasNFR ? '✅' : '❌';
      
      console.log(`${status} Q${q.id}: ${q.title}`);
      console.log(`   FR: ${q.functional_requirements?.length || 0} items`);
      console.log(`   NFR: ${q.non_functional_requirements?.length || 0} items`);
      
      if (hasFR) {
        console.log(`   FR Sample: "${q.functional_requirements![0]}"`);
      }
      if (hasNFR) {
        console.log(`   NFR Sample: "${q.non_functional_requirements![0]}"`);
      }
    });
    
    // Test 2: Load a specific question
    console.log('\n🔍 Testing getQuestionById for question 26...');
    const question26 = await QuestionService.getQuestionById('26');
    
    if (question26) {
      console.log('✅ Successfully loaded question 26');
      console.log(`   Title: ${question26.title}`);
      console.log(`   FR: ${question26.functional_requirements?.length || 0} items`);
      console.log(`   NFR: ${question26.non_functional_requirements?.length || 0} items`);
      
      if (question26.functional_requirements && question26.functional_requirements.length > 0) {
        console.log('   FR Items:');
        question26.functional_requirements.forEach((fr, i) => {
          console.log(`     ${i + 1}. ${fr}`);
        });
      }
      
      if (question26.non_functional_requirements && question26.non_functional_requirements.length > 0) {
        console.log('   NFR Items:');
        question26.non_functional_requirements.forEach((nfr, i) => {
          console.log(`     ${i + 1}. ${nfr}`);
        });
      }
    } else {
      console.log('❌ Failed to load question 26');
    }
    
    // Summary
    const questionsWithFR = questions.filter(q => q.functional_requirements && q.functional_requirements.length > 0).length;
    const questionsWithNFR = questions.filter(q => q.non_functional_requirements && q.non_functional_requirements.length > 0).length;
    
    console.log('\n📊 Summary:');
    console.log(`Total questions: ${questions.length}`);
    console.log(`Questions with FR: ${questionsWithFR}`);
    console.log(`Questions with NFR: ${questionsWithNFR}`);
    console.log(`Questions with both FR & NFR: ${questions.filter(q => 
      q.functional_requirements && q.functional_requirements.length > 0 &&
      q.non_functional_requirements && q.non_functional_requirements.length > 0
    ).length}`);
    
    return {
      success: true,
      totalQuestions: questions.length,
      questionsWithFR,
      questionsWithNFR,
      testQuestions
    };
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};

// Export for console use
(window as any).testQuestionServiceFRNFR = testQuestionServiceFRNFR;
