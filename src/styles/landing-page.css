@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
@import "tw-animate-css";

/* Layrs Landing Page Styles */
:root {
  /* Layrs brand colors */
  --color-layrs-blue: #3b82f6;
  --color-layrs-purple: #8b5cf6;
  --color-layrs-gradient: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  
  /* Professional color palette */
  --color-background: #ffffff;
  --color-foreground: #1a1a2e;
  --color-card: #ffffff;
  --color-card-foreground: #1a1a2e;
  --color-popover: #ffffff;
  --color-popover-foreground: #1a1a2e;
  --color-primary: #6366f1;
  --color-primary-foreground: #ffffff;
  --color-secondary: #f8fafc;
  --color-secondary-foreground: #1a1a2e;
  --color-muted: #f1f5f9;
  --color-muted-foreground: #64748b;
  --color-accent: #8b5cf6;
  --color-accent-foreground: #ffffff;
  --color-destructive: #ef4444;
  --color-destructive-foreground: #fafafa;
  --color-border: #e2e8f0;
  --color-input: #e2e8f0;
  --color-ring: #6366f1;
  
  /* Chart colors */
  --color-chart-1: #6366f1;
  --color-chart-2: #8b5cf6;
  --color-chart-3: #06b6d4;
  --color-chart-4: #10b981;
  --color-chart-5: #f59e0b;
  
  /* Sidebar colors */
  --color-sidebar: #f8fafc;
  --color-sidebar-foreground: #64748b;
  --color-sidebar-primary: #6366f1;
  --color-sidebar-primary-foreground: #ffffff;
  --color-sidebar-accent: #f1f5f9;
  --color-sidebar-accent-foreground: #1a1a2e;
  --color-sidebar-border: #e2e8f0;
  --color-sidebar-ring: #6366f1;
  
  /* Border radius */
  --radius-lg: 0.75rem;
  --radius-md: calc(0.75rem - 2px);
  --radius-sm: calc(0.75rem - 4px);
  
  /* Animations */
  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;
  
  /* Font */
  --font-inter: "Inter", sans-serif;
}

.dark {
  --color-background: #0f0f23;
  --color-foreground: #f1f5f9;
  --color-card: #1e1e40;
  --color-card-foreground: #f1f5f9;
  --color-muted: #2d2d5a;
  --color-muted-foreground: #94a3b8;
  --color-border: #2d2d5a;
}

@keyframes accordion-down {
  from {
    height: 0;
  }
  to {
    height: var(--radix-accordion-content-height);
  }
}

@keyframes accordion-up {
  from {
    height: var(--radix-accordion-content-height);
  }
  to {
    height: 0;
  }
}

@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

/* Utility classes */
.gradient-layrs {
  background: var(--color-layrs-gradient);
}

.text-gradient-layrs {
  background: var(--color-layrs-gradient);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Animation classes */
.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

/* Typography */
.font-inter {
  font-family: var(--font-inter);
}

.text-display {
  font-size: 3rem;
  font-weight: 700;
}

.text-h1 {
  font-size: 2.25rem;
  font-weight: 700;
}

.text-h2 {
  font-size: 1.5rem;
  font-weight: 700;
}

.text-h3 {
  font-size: 1.25rem;
  font-weight: 700;
}

.text-body {
  font-size: 1rem;
  font-weight: 400;
  color: var(--color-muted-foreground);
}

.text-caption {
  font-size: 0.875rem;
  font-weight: 400;
  color: var(--color-muted-foreground);
}

/* Base styles for landing page */
.landing-page {
  font-family: var(--font-inter);
  line-height: normal;
}

.landing-page h1, 
.landing-page h2, 
.landing-page h3, 
.landing-page h4, 
.landing-page h5, 
.landing-page h6 {
  font-weight: 700;
  color: var(--color-foreground);
}

.landing-page h1 {
  font-size: 5rem;
  line-height: 1.2;
}

.landing-page h2 {
  font-size: 1.5rem;
  line-height: 1.3;
}

.landing-page h3 {
  font-size: 1.25rem;
  line-height: 1.4;
}

.landing-page p {
  font-size: 1.2rem;
  line-height: 1.5;
  color: #000;
}

.landing-page a {
  color: var(--color-primary);
  text-decoration: none;
}

.landing-page a:hover {
  text-decoration: underline;
}

/* Stats section specific styles */
.stats-number {
  font-size: 5rem;
  font-weight: 700;
  line-height: 1;
  color: #000;
  font-family: var(--font-inter);
}

.stats-label {
  font-size: 2rem;
  font-weight: 700;
  line-height: 1.2;
  color: #6366f1;
  font-family: var(--font-inter);
}

/* Hero section specific styles */
.hero-subtitle {
  font-size: 2.25rem;
  font-weight: 500;
  line-height: 1.2;
  color: #000;
  font-family: var(--font-inter);
}

@media (min-width: 640px) {
  .hero-subtitle {
    font-size: 3rem;
  }
}

@media (min-width: 1024px) {
  .hero-subtitle {
    font-size: 3rem;
  }
}
