
import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Toaster } from '@/components/ui/sonner';

// Context Providers
import { AuthProvider } from '@/contexts/AuthContext';
import { DialogProvider } from '@/contexts/DialogContext';
import { QuestionsProvider } from '@/contexts/QuestionsContext';
import { DesignProvider } from '@/contexts/DesignContext';
import { CourseProvider } from '@/contexts/CourseContext';
import { EdgeTypeProvider } from '@/contexts/EdgeTypeContext';

// Pages
import Index from '@/pages/Index';
import Questions from '@/pages/Questions';
import QuestionDetail from '@/pages/QuestionDetail';
import Register from '@/pages/Register';
import Login from '@/pages/Login';
import ForgotPassword from '@/pages/ForgotPassword';
import ResetPassword from '@/pages/ResetPassword';
import VerifyEmail from '@/pages/VerifyEmail';
import Profile from '@/pages/Profile';
import NotFound from '@/pages/NotFound';
import LandingPage from '@/pages/LandingPage';
import CleanLandingPage from '@/pages/CleanLandingPage';
import ModernLandingPage from '@/pages/ModernLandingPage';
import MagicalLandingPage from '@/pages/MagicalLandingPage';
import NewLandingPage from '@/pages/NewLandingPage';
import GuidedMode from '@/pages/GuidedMode';
import BetaInvitation from '@/pages/BetaInvitation';
import Development from '@/pages/Development';

// Admin Pages
import AnalyticsAdmin from '@/pages/AnalyticsAdmin';
import WaitlistAdmin from '@/pages/WaitlistAdmin';
import FeedbackAnalytics from '@/pages/admin/FeedbackAnalytics';

// Test Pages  
import DesignTest from '@/pages/DesignTest';
import AgentTest from '@/pages/AgentTest';

// Components
import DesignMigration from '@/components/DesignMigration';
import ProtectedRoute from '@/components/ProtectedRoute';
import AdminGuard from '@/components/AdminGuard';
import BetaAccessGuard from '@/components/BetaAccessGuard';

function App() {
  return (
    <Router>
      <AuthProvider>
        <DialogProvider>
          <EdgeTypeProvider>
            <QuestionsProvider>
              <DesignProvider>
                <CourseProvider>
                  <DesignMigration />
                  <Toaster />
                  <Routes>
                    <Route path="/" element={<LandingPage />} />
                    <Route path="/inspolandingpage" element={<CleanLandingPage />} />
                    <Route path="/landingpage" element={<NewLandingPage />} />
                    <Route path="/development" element={<Development />} />
                    <Route path="/questions" element={<BetaAccessGuard><Questions /></BetaAccessGuard>} />
                    <Route path="/questions/:questionId" element={<BetaAccessGuard><QuestionDetail /></BetaAccessGuard>} />
                    <Route path="/design/:questionId" element={<BetaAccessGuard><Index /></BetaAccessGuard>} />
                    <Route path="/canvas" element={<BetaAccessGuard><Index /></BetaAccessGuard>} />
                    <Route path="/register" element={<Register />} />
                    <Route path="/login" element={<Login />} />
                    <Route path="/forgot-password" element={<ForgotPassword />} />
                    <Route path="/reset-password" element={<ResetPassword />} />
                    <Route path="/verify-email" element={<VerifyEmail />} />
                    <Route path="/profile" element={<ProtectedRoute><Profile /></ProtectedRoute>} />
                    <Route path="/guided/:courseId" element={<BetaAccessGuard><GuidedMode /></BetaAccessGuard>} />
                    <Route path="/beta-invitation" element={<BetaInvitation />} />

                    {/* Admin Routes */}
                    <Route path="/admin/analytics" element={<AdminGuard><AnalyticsAdmin /></AdminGuard>} />
                    <Route path="/admin/waitlist" element={<AdminGuard><WaitlistAdmin /></AdminGuard>} />
                    <Route path="/admin/feedback" element={<AdminGuard><FeedbackAnalytics /></AdminGuard>} />

                    {/* Test Routes */}
                    <Route path="/test/design" element={<DesignTest />} />
                    <Route path="/test/agent" element={<AgentTest />} />

                    <Route path="*" element={<NotFound />} />
                  </Routes>
                </CourseProvider>
              </DesignProvider>
            </QuestionsProvider>
          </EdgeTypeProvider>
        </DialogProvider>
      </AuthProvider>
    </Router>
  );
}

export default App;
