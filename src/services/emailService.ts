import { supabase } from '@/lib/supabase';
import { debugLog, debugWarn, debugError } from '@/utils/debugLogger';

export interface EmailTemplate {
  subject: string;
  htmlContent: string;
  textContent: string;
}

export interface BetaInvitationEmailData {
  email: string;
  invitationToken: string;
  expiresAt: string;
}

/**
 * Generate beta invitation email template
 */
export const generateBetaInvitationEmail = (data: BetaInvitationEmailData): EmailTemplate => {
  const invitationUrl = `https://layrs.me/beta-invitation?token=${data.invitationToken}`;
  const expirationDate = new Date(data.expiresAt).toLocaleDateString();

  const htmlContent = `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>You're Invited to Layrs Beta!</title>
        <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { text-align: center; margin-bottom: 30px; }
            .logo { font-size: 24px; font-weight: bold; color: #2563eb; }
            .content { background: #f8fafc; padding: 30px; border-radius: 8px; margin: 20px 0; }
            .button { display: inline-block; background: #2563eb; color: #ffffff !important; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 500; }
            .button:hover { background: #1d4ed8; color: #ffffff !important; }
            .footer { text-align: center; margin-top: 30px; font-size: 14px; color: #666; }
            .expiry { background: #fef3c7; padding: 15px; border-radius: 6px; margin: 20px 0; border-left: 4px solid #f59e0b; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🎉 You're Invited to Beta!</h1>
            </div>

            <div class="content">
                <p>Congratulations! You've been selected to join the <strong>Layrs Beta Program</strong>.</p>

                <p>Layrs is the next-generation system design learning platform that helps you master system architecture through interactive, hands-on practice.</p>

                <h3>What you'll get access to:</h3>
                <ul>
                    <li>🎨 <strong>Interactive Design Canvas</strong> - Build system architectures visually</li>
                    <li>💡 <strong>Real-world Problems</strong> - Practice with actual interview questions</li>
                    <li>🤖 <strong>AI Assessment</strong> - Get instant feedback on your designs</li>
                    <li>📊 <strong>Progress Tracking</strong> - Monitor your learning journey</li>
                </ul>

                <div style="text-align: center; margin: 30px 0;">
                    <a href="${invitationUrl}" class="button">Accept Beta Invitation</a>
                </div>

                <div class="expiry">
                    <strong>⏰ Important:</strong> This invitation expires on <strong>${expirationDate}</strong>.
                    Make sure to accept it before then!
                </div>
            </div>

            <div class="footer">
                <p>If you have any questions, feel free to reach out to our team.</p>
                <p>Welcome to the future of system design learning!</p>
                <p><strong>The Layrs Team</strong></p>
            </div>
        </div>
    </body>
    </html>
  `;

  const textContent = `
You're Invited to Layrs Beta!

Congratulations! You've been selected to join the Layrs Beta Program.

Layrs is the next-generation system design learning platform that helps you master system architecture through interactive, hands-on practice.

What you'll get access to:
• Interactive Design Canvas - Build system architectures visually
• Guided Courses - Step-by-step system design tutorials
• Real-world Problems - Practice with actual interview questions
• AI Assessment - Get instant feedback on your designs
• Progress Tracking - Monitor your learning journey

Accept your invitation: ${invitationUrl}

⏰ Important: This invitation expires on ${expirationDate}. Make sure to accept it before then!

If you have any questions, feel free to reach out to our team.
Welcome to the future of system design learning!

The Layrs Team
  `;

  return {
    subject: '🎉 You\'re Invited to Layrs Beta - System Design Learning Platform',
    htmlContent,
    textContent
  };
};

/**
 * Send beta invitation email using Supabase Edge Function
 */
export const sendBetaInvitationEmail = async (data: BetaInvitationEmailData): Promise<{ success: boolean; error?: string }> => {
  try {
    const emailTemplate = generateBetaInvitationEmail(data);

    // Try to call Supabase Edge Function first
    try {
      const { data: result, error } = await supabase.functions.invoke('send-email', {
        body: {
          to: data.email,
          subject: emailTemplate.subject,
          html: emailTemplate.htmlContent,
          text: emailTemplate.textContent,
          type: 'beta-invitation',
          token: data.invitationToken,
          expiresAt: data.expiresAt
        }
      });

      if (error) {
        debugWarn('Edge Function failed, using fallback:', error);
        return await sendBetaInvitationEmailFallback(data);
      }

      return { success: true };
    } catch (edgeFunctionError) {
      debugWarn('Edge Function not available, using fallback:', edgeFunctionError);
      return await sendBetaInvitationEmailFallback(data);
    }
  } catch (error) {
    debugError('Error sending beta invitation email:', error);
    return { success: false, error: 'Failed to send invitation email' };
  }
};

/**
 * Send multiple beta invitation emails
 */
export const sendBetaInvitationEmails = async (invitations: BetaInvitationEmailData[]): Promise<{
  success: boolean;
  results: Array<{ email: string; success: boolean; error?: string }>;
  totalSent: number;
  totalFailed: number;
}> => {
  const results: Array<{ email: string; success: boolean; error?: string }> = [];

  // Process each invitation sequentially, with a 1 second delay between sends
  for (const invitation of invitations) {
    const result = await sendBetaInvitationEmail(invitation);
    results.push({
      email: invitation.email,
      success: result.success,
      error: result.error
    });
    // Wait for 1 second before sending the next email
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  const totalSent = results.filter(r => r.success).length;
  const totalFailed = results.filter(r => !r.success).length;

  return {
    success: totalSent > 0,
    results,
    totalSent,
    totalFailed
  };
};

/**
 * Fallback email sending using a simple HTTP service (for development)
 */
export const sendBetaInvitationEmailFallback = async (data: BetaInvitationEmailData): Promise<{ success: boolean; error?: string }> => {
  try {
    const emailTemplate = generateBetaInvitationEmail(data);

    // For development, we'll just log the email content
    debugLog('📧 Beta Invitation Email (Development Mode)');
    debugLog('To:', data.email);
    debugLog('Subject:', emailTemplate.subject);
    debugLog('Invitation URL:', `${window.location.origin}/beta-invitation?token=${data.invitationToken}`);
    debugLog('Expires:', new Date(data.expiresAt).toLocaleDateString());

    // In a real implementation, you would integrate with:
    // - SendGrid
    // - Mailgun
    // - AWS SES
    // - Resend
    // - Or any other email service

    // For now, simulate success
    return { success: true };
  } catch (error) {
    debugError('Error in fallback email sending:', error);
    return { success: false, error: 'Failed to send invitation email' };
  }
};
