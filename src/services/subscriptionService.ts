import { supabase } from '@/lib/supabase';
import { debugLog, debugWarn, debugError } from '@/utils/debugLogger';

export interface Subscription {
  id: string;
  subscription_id: string;
  product_id: string;
  status: string;
  recurring_pre_tax_amount: number;
  currency: string;
  quantity: number;
  next_billing_date?: string;
  trial_period_days: number;
  addons: any[];
  customer: any;
  metadata: any;
}

export interface PlanInfo {
  id: string;
  name: string;
  price: number;
  credits: number;
  period: string;
}

export interface CreateSubscriptionRequest {
  plan_type: 'pro';
  customer_data: {
    name: string;
    email: string;
  };
  billing_cycle?: 'monthly' | 'yearly';
  billing_address?: {
    street?: string;
    city?: string;
    state?: string;
    country?: string;
    zipcode?: string;
  };
  return_url?: string;
}

export interface CreateSubscriptionResponse {
  success: boolean;
  payment_link?: string;
  subscription_id?: string;
  error?: string;
}

export interface UserData {
  subscription: Subscription | null;
  planInfo: PlanInfo | null;
  credits: number;
  isProUser: boolean;
  canUpgrade: boolean;
}

class SubscriptionService {
  /**
   * Check if the current user has an active subscription
   */
  async hasActiveSubscription(userId?: string): Promise<boolean> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      const targetUserId = userId || user?.id;

      debugLog(`Checking subscription for user: ${targetUserId}`);

      if (!targetUserId) {
        debugWarn('No user ID provided for subscription check');
        // For development: if no user, allow access to test the system
        return process.env.NODE_ENV === 'development';
      }

      const { data: subscriptions, error } = await supabase
        .from('subscriptions')
        .select('status, product_id')
        .eq('user_id', targetUserId)
        .eq('status', 'active');

      debugLog(`Subscription query result:`, { subscriptions, error });

      if (error) {
        debugError('Error checking subscription status:', error);
        // For development: if error, allow access to test the system
        return process.env.NODE_ENV === 'development';
      }

      const hasActiveSubscription = subscriptions && subscriptions.length > 0;
      debugLog(`User ${targetUserId} has active subscription: ${hasActiveSubscription}`);
      return hasActiveSubscription;
    } catch (error) {
      debugError('Error in hasActiveSubscription:', error);
      // For development: if error, allow access to test the system
      return process.env.NODE_ENV === 'development';
    }
  }

  /**
   * Get comprehensive user data including subscription, plan info, and credits
   */
  async getUserData(userId?: string): Promise<UserData> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      const targetUserId = userId || user?.id;
      
      if (!targetUserId) {
        throw new Error('No user ID available');
      }

      // Call the get-user-data Supabase function
      const { data, error } = await supabase.functions.invoke('get-user-data', {
        body: { user_id: targetUserId }
      });

      if (error) {
        debugError('Error fetching user data:', error);
        throw error;
      }

      const userData = data as UserData;
      debugLog('Fetched user data:', userData);
      return userData;
    } catch (error) {
      debugError('Error in getUserData:', error);
      // Return default data for basic users
      return {
        subscription: null,
        planInfo: {
          id: 'basic',
          name: 'Basic Plan',
          price: 0,
          credits: 10,
          period: 'monthly'
        },
        credits: 10,
        isProUser: false,
        canUpgrade: true
      };
    }
  }

  /**
   * Create a new subscription
   */
  async createSubscription(request: CreateSubscriptionRequest): Promise<CreateSubscriptionResponse> {
    try {
      debugLog('Creating subscription:', request);

      const { data, error } = await supabase.functions.invoke('create-subscription', {
        body: request
      });

      if (error) {
        debugError('Error creating subscription:', error);
        throw error;
      }

      debugLog('Subscription created successfully:', data);
      return data as CreateSubscriptionResponse;
    } catch (error) {
      debugError('Error in createSubscription:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Format price for display
   */
  formatPrice(price: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  }

  /**
   * Check if user can access a specific question
   * Basic users can only access URL shortener (question ID: 1)
   * Pro users can access all questions
   */
  async canAccessQuestion(questionId: number, userId?: string): Promise<boolean> {
    try {
      debugLog(`Checking access for question ${questionId}`);

      // URL shortener question (ID: 1) is always accessible
      if (questionId === 1) {
        debugLog(`Question ${questionId} is URL shortener - access granted`);
        return true;
      }

      // For all other questions, check if user has active subscription
      const hasSubscription = await this.hasActiveSubscription(userId);
      debugLog(`Question ${questionId} subscription check result: ${hasSubscription}`);
      return hasSubscription;
    } catch (error) {
      debugError('Error checking question access:', error);
      return false;
    }
  }

  /**
   * Get list of accessible question IDs for a user
   */
  async getAccessibleQuestionIds(userId?: string): Promise<number[]> {
    try {
      const hasSubscription = await this.hasActiveSubscription(userId);

      if (hasSubscription) {
        // Pro users can access all questions - return null to indicate no filtering needed
        return [];
      } else {
        // Basic users can only access URL shortener
        return [1];
      }
    } catch (error) {
      debugError('Error getting accessible question IDs:', error);
      return [1]; // Default to basic access
    }
  }
}

export const subscriptionService = new SubscriptionService();
