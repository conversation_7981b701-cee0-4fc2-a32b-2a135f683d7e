import { supabase } from '@/lib/supabase';
import { Question } from '@/types/Question';
import { debugLog, debugWarn, debugError } from '@/utils/debugLogger';

// Admin emails that can manage questions
const ADMIN_EMAILS = [
  '<EMAIL>',
  // Add more admin emails here if needed
];

// Database table name
const QUESTIONS_TABLE = 'questions';

// Interface for the question record in the database
interface QuestionRecord {
  id: number;
  title: string;
  description: string;
  difficulty: 'easy' | 'medium' | 'hard';
  category: string;
  tags: string[];
  requirements: string[];
  functional_requirements?: string[];  // New FR column
  non_functional_requirements?: string[];  // New NFR column
  constraints: string[];
  hints: string[];
  sample_solution?: string;
  is_active: boolean;
  created_by?: string;
  created_at: string;
  updated_at: string;
}

export class QuestionService {
  /**
   * Check if current user is admin
   */
  static async isCurrentUserAdmin(): Promise<boolean> {
    try {
      const { data: { user } } = await supabase.auth.getUser();

      if (!user?.email) {
        return false;
      }

      // Check against hardcoded admin emails
      const isHardcodedAdmin = ADMIN_EMAILS.includes(user.email.toLowerCase());

      if (isHardcodedAdmin) {
        return true;
      }

      // Also check admin_users table
      const { data: adminUser } = await supabase
        .from('admin_users')
        .select('id')
        .eq('email', user.email.toLowerCase())
        .eq('is_active', true)
        .single();

      return !!adminUser;
    } catch (error) {
      console.error('Error checking admin status:', error);
      return false;
    }
  }

  /**
   * Validate admin access for write operations
   */
  static async validateAdminAccess(): Promise<{ isAdmin: boolean; error?: string }> {
    const isAdmin = await this.isCurrentUserAdmin();

    if (!isAdmin) {
      return {
        isAdmin: false,
        error: 'Admin access required. Only administrators can create, update, or delete questions.'
      };
    }

    return { isAdmin: true };
  }

  /**
   * Check if questions table exists and create it if it doesn't
   */
  static async ensureTableExists(): Promise<boolean> {
    try {
      // Try to query the table to see if it exists
      const { error } = await supabase
        .from(QUESTIONS_TABLE)
        .select('id')
        .limit(1);

      if (error && error.code === 'PGRST116') {
        // Table doesn't exist, we need to create it manually in Supabase dashboard
        console.warn('Questions table does not exist. Please create it in Supabase dashboard.');
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error checking table existence:', error);
      return false;
    }
  }

  /**
   * Check if questions exist in database
   */
  static async checkQuestionsExist(): Promise<{ success: boolean; count: number; error?: string }> {
    try {
      debugLog('🔍 Checking if questions exist in database...');

      // Check if table exists
      const tableExists = await this.ensureTableExists();
      if (!tableExists) {
        return {
          success: false,
          count: 0,
          error: 'Questions table does not exist. Please create it in Supabase dashboard first.'
        };
      }

      // Check if questions exist
      const { data: existingQuestions, error } = await supabase
        .from(QUESTIONS_TABLE)
        .select('id', { count: 'exact' });

      if (error) {
        console.error('❌ Failed to check questions:', error);
        return { success: false, count: 0, error: error.message };
      }

      const count = existingQuestions?.length || 0;
      debugLog(`📊 Found ${count} questions in database`);

      return { success: true, count };

    } catch (error) {
      console.error('❌ Error checking questions:', error);
      return {
        success: false,
        count: 0,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Load all questions from Supabase
   */
  static async loadQuestions(): Promise<Question[]> {
    try {
      const { data, error } = await supabase
        .from(QUESTIONS_TABLE)
        .select('*')
        .eq('is_active', true)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error loading questions from Supabase:', error);
        throw new Error(`Failed to load questions: ${error.message}`);
      }

      if (!data || data.length === 0) {
        debugLog('📋 No questions found in database');
        return [];
      }

      // Transform database records to Question interface
      const questions: Question[] = data.map(record => ({
        id: record.id,
        title: record.title,
        description: record.description,
        difficulty: record.difficulty,
        category: record.category as Question['category'],
        tags: record.tags || [],
        requirements: record.requirements || [],
        functional_requirements: record.functional_requirements || undefined,  // New FR field
        non_functional_requirements: record.non_functional_requirements || undefined,  // New NFR field
        constraints: record.constraints || [],
        hints: record.hints || [],
        sampleSolution: record.sample_solution,
        createdAt: record.created_at,
        updatedAt: record.updated_at
      }));

      debugLog(`✅ Loaded ${questions.length} questions from Supabase`);
      return questions;

    } catch (error) {
      console.error('Error in loadQuestions:', error);
      throw error;
    }
  }

  /**
   * Get a specific question by ID
   */
  static async getQuestionById(id: number): Promise<Question | null> {
    try {
      const { data, error } = await supabase
        .from(QUESTIONS_TABLE)
        .select('*')
        .eq('id', id)
        .eq('is_active', true)
        .single();

      if (error || !data) {
        console.error('Question not found:', error);
        return null;
      }

      // Transform database record to Question interface
      const question: Question = {
        id: data.id,
        title: data.title,
        description: data.description,
        difficulty: data.difficulty,
        category: data.category as Question['category'],
        tags: data.tags || [],
        requirements: data.requirements || [],
        functional_requirements: data.functional_requirements || undefined,  // New FR field
        non_functional_requirements: data.non_functional_requirements || undefined,  // New NFR field
        constraints: data.constraints || [],
        hints: data.hints || [],
        sampleSolution: data.sample_solution,
        createdAt: data.created_at,
        updatedAt: data.updated_at
      };

      return question;

    } catch (error) {
      console.error('Error getting question by ID:', error);
      return null;
    }
  }

  /**
   * Create a new question (Admin only)
   */
  static async createQuestion(question: Omit<Question, 'id' | 'createdAt' | 'updatedAt'>): Promise<Question | null> {
    try {
      // Validate admin access
      const { isAdmin, error: adminError } = await this.validateAdminAccess();
      if (!isAdmin) {
        console.error('Create question failed:', adminError);
        throw new Error(adminError);
      }

      const { data, error } = await supabase
        .from(QUESTIONS_TABLE)
        .insert({
          title: question.title,
          description: question.description,
          difficulty: question.difficulty,
          category: question.category,
          tags: question.tags,
          requirements: question.requirements,
          constraints: question.constraints || [],
          hints: question.hints || [],
          sample_solution: question.sampleSolution,
          is_active: true
        })
        .select()
        .single();

      if (error || !data) {
        console.error('Error creating question:', error);
        return null;
      }

      // Transform database record to Question interface
      const newQuestion: Question = {
        id: data.id,
        title: data.title,
        description: data.description,
        difficulty: data.difficulty,
        category: data.category as Question['category'],
        tags: data.tags || [],
        requirements: data.requirements || [],
        constraints: data.constraints || [],
        hints: data.hints || [],
        sampleSolution: data.sample_solution,
        createdAt: data.created_at,
        updatedAt: data.updated_at
      };

      return newQuestion;

    } catch (error) {
      console.error('Error creating question:', error);
      return null;
    }
  }

  /**
   * Update an existing question (Admin only)
   */
  static async updateQuestion(id: number, updates: Partial<Omit<Question, 'id' | 'createdAt' | 'updatedAt'>>): Promise<Question | null> {
    try {
      // Validate admin access
      const { isAdmin, error: adminError } = await this.validateAdminAccess();
      if (!isAdmin) {
        console.error('Update question failed:', adminError);
        throw new Error(adminError);
      }

      const updateData: any = {};
      
      if (updates.title) updateData.title = updates.title;
      if (updates.description) updateData.description = updates.description;
      if (updates.difficulty) updateData.difficulty = updates.difficulty;
      if (updates.category) updateData.category = updates.category;
      if (updates.tags) updateData.tags = updates.tags;
      if (updates.requirements) updateData.requirements = updates.requirements;
      if (updates.constraints) updateData.constraints = updates.constraints;
      if (updates.hints) updateData.hints = updates.hints;
      if (updates.sampleSolution !== undefined) updateData.sample_solution = updates.sampleSolution;

      const { data, error } = await supabase
        .from(QUESTIONS_TABLE)
        .update(updateData)
        .eq('id', id)
        .select()
        .single();

      if (error || !data) {
        console.error('Error updating question:', error);
        return null;
      }

      // Transform database record to Question interface
      const updatedQuestion: Question = {
        id: data.id,
        title: data.title,
        description: data.description,
        difficulty: data.difficulty,
        category: data.category as Question['category'],
        tags: data.tags || [],
        requirements: data.requirements || [],
        constraints: data.constraints || [],
        hints: data.hints || [],
        sampleSolution: data.sample_solution,
        createdAt: data.created_at,
        updatedAt: data.updated_at
      };

      return updatedQuestion;

    } catch (error) {
      console.error('Error updating question:', error);
      return null;
    }
  }

  /**
   * Delete a question (Admin only - soft delete by setting is_active to false)
   */
  static async deleteQuestion(id: number): Promise<boolean> {
    try {
      // Validate admin access
      const { isAdmin, error: adminError } = await this.validateAdminAccess();
      if (!isAdmin) {
        console.error('Delete question failed:', adminError);
        throw new Error(adminError);
      }

      const { error } = await supabase
        .from(QUESTIONS_TABLE)
        .update({ is_active: false })
        .eq('id', id);

      if (error) {
        console.error('Error deleting question:', error);
        return false;
      }

      return true;

    } catch (error) {
      console.error('Error deleting question:', error);
      return false;
    }
  }

  /**
   * Get database connection status
   */
  static async getConnectionStatus(): Promise<{ connected: boolean; tableExists: boolean; recordCount: number }> {
    try {
      const tableExists = await this.ensureTableExists();
      
      if (!tableExists) {
        return { connected: true, tableExists: false, recordCount: 0 };
      }

      const { data, error } = await supabase
        .from(QUESTIONS_TABLE)
        .select('id', { count: 'exact' });

      return {
        connected: !error,
        tableExists: true,
        recordCount: data?.length || 0
      };

    } catch (error) {
      return { connected: false, tableExists: false, recordCount: 0 };
    }
  }
}
