import { supabase } from '@/lib/supabase';
import { sendBetaInvitationEmails, BetaInvitationEmailData } from './emailService';
import { debugLog, debugWarn, debugError } from '@/utils/debugLogger';

const WAITLIST_TABLE = 'waitlist';
const BETA_INVITATIONS_TABLE = 'beta_invitations';

// Types
export interface WaitlistEntry {
  id: string;
  email: string;
  referral_code?: string;
  beta_status: 'pending' | 'invited' | 'accepted' | 'declined';
  beta_invited_at?: string;
  beta_accessed_at?: string;
  beta_token?: string;
  created_at: string;
  updated_at: string;
}

export interface BetaInvitation {
  id: string;
  waitlist_id: string;
  email: string;
  invitation_token: string;
  invited_by: string;
  invited_at: string;
  expires_at: string;
  status: 'sent' | 'opened' | 'accepted' | 'expired';
  accepted_at?: string;
  created_at: string;
}

/**
 * Get all waitlist entries with beta status
 */
export const getWaitlistWithBetaStatus = async (): Promise<WaitlistEntry[]> => {
  try {
    const { data, error } = await supabase
      .from(WAITLIST_TABLE)
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      debugError('Error fetching waitlist:', error);
      return [];
    }

    return data as WaitlistEntry[];
  } catch (error) {
    debugError('Error fetching waitlist:', error);
    return [];
  }
};

/**
 * Generate a secure invitation token
 */
const generateInvitationToken = (): string => {
  return btoa(`${Date.now()}-${Math.random()}`).replace(/[^a-zA-Z0-9]/g, '').substring(0, 32);
};

const getEntriesFromWaitlist = async (userIds: string[], random: boolean = false): Promise<{ waitlistEntries: WaitlistEntry[], fetchError: any }> => {
  if (!random) {
    const { data: waitlistEntries, error: fetchError } = await supabase
      .from(WAITLIST_TABLE)
      .select('*')
      .in('id', userIds);
    return { waitlistEntries, fetchError };
  } else {
    console.log('Getting random entries from waitlist');
    const { data: waitlistEntries, error: fetchError } = await supabase
      .from(WAITLIST_TABLE)
      .select('*')
      .in('beta_status', ['pending'])
      .order('created_at', { ascending: false })
      .limit(90);
    return { waitlistEntries, fetchError };
  }
}

/**
 * Invite users to beta
 */
export const inviteUsersToBeta = async (
  userIds: string[],
  invitedBy: string,
  random: boolean = false
): Promise<{ 
  success: boolean; 
  error?: string; 
  invitations?: BetaInvitation[];
  emailResults?: {
    totalSent: number;
    totalFailed: number;
    details: Array<{ email: string; success: boolean; error?: string }>;
  };
}> => {
  try {
    const { waitlistEntries, fetchError } = await getEntriesFromWaitlist(userIds, random);

    console.log('waitlistEntries', waitlistEntries);

    if (fetchError) {
      return { success: false, error: fetchError.message };
    }

    if (!waitlistEntries || waitlistEntries.length === 0) {
      return { success: false, error: 'No valid users found' };
    }

    // Create invitations for each user
    const invitations: Partial<BetaInvitation>[] = waitlistEntries.map(entry => ({
      waitlist_id: entry.id,
      email: entry.email,
      invitation_token: generateInvitationToken(),
      invited_by: invitedBy,
      expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days from now
    }));

    // Insert invitations
    const { data: createdInvitations, error: inviteError } = await supabase
      .from(BETA_INVITATIONS_TABLE)
      .insert(invitations)
      .select();

    if (inviteError) {
      return { success: false, error: inviteError.message };
    }

    // Update waitlist entries to mark as invited
    // We need to update each entry individually since we're updating existing records
    const updatePromises = waitlistEntries.map(async (entry, index) => {
      return supabase
        .from(WAITLIST_TABLE)
        .update({
          beta_status: 'invited' as const,
          beta_invited_at: new Date().toISOString(),
          beta_token: invitations[index].invitation_token,
          updated_at: new Date().toISOString()
        })
        .eq('id', entry.id);
    });

    const updateResults = await Promise.all(updatePromises);
    const updateError = updateResults.find(result => result.error)?.error;

    if (updateError) {
      return { success: false, error: updateError.message };
    }

    // Send invitation emails
    const emailData: BetaInvitationEmailData[] = createdInvitations.map(invitation => ({
      email: invitation.email,
      invitationToken: invitation.invitation_token,
      expiresAt: invitation.expires_at
    }));

    const emailResults = await sendBetaInvitationEmails(emailData);

    // Log email sending results
    debugLog(`📧 Email Results: ${emailResults.totalSent} sent, ${emailResults.totalFailed} failed`);

    if (emailResults.totalFailed > 0) {
      debugWarn('Some emails failed to send:', emailResults.results.filter(r => !r.success));
    }

    return {
      success: true,
      invitations: createdInvitations as BetaInvitation[],
      emailResults: {
        totalSent: emailResults.totalSent,
        totalFailed: emailResults.totalFailed,
        details: emailResults.results
      }
    };
  } catch (error) {
    debugError('Error inviting users to beta:', error);
    return {
      success: false,
      error: 'An unexpected error occurred while sending invitations'
    };
  }
};

/**
 * Get beta invitation by token
 */
export const getBetaInvitationByToken = async (
  token: string
): Promise<{ success: boolean; invitation?: BetaInvitation; error?: string }> => {
  try {
    const { data, error } = await supabase
      .from(BETA_INVITATIONS_TABLE)
      .select('*')
      .eq('invitation_token', token)
      .single();

    if (error) {
      return { success: false, error: 'Invalid or expired invitation' };
    }

    // Check if invitation has expired
    const invitation = data as BetaInvitation;
    if (new Date(invitation.expires_at) < new Date()) {
      return { success: false, error: 'Invitation has expired' };
    }

    return { success: true, invitation };
  } catch (error) {
    debugError('Error fetching beta invitation:', error);
    return { success: false, error: 'Failed to validate invitation' };
  }
};

/**
 * Accept beta invitation
 */
export const acceptBetaInvitation = async (
  token: string
): Promise<{ success: boolean; error?: string }> => {
  try {
    // Get the invitation
    const { success, invitation, error } = await getBetaInvitationByToken(token);

    if (!success || !invitation) {
      return { success: false, error: error || 'Invalid invitation' };
    }

    // Update invitation status
    const { error: updateInviteError } = await supabase
      .from(BETA_INVITATIONS_TABLE)
      .update({
        status: 'accepted',
        accepted_at: new Date().toISOString()
      })
      .eq('invitation_token', token);

    if (updateInviteError) {
      return { success: false, error: updateInviteError.message };
    }

    // Update waitlist entry
    const { error: updateWaitlistError } = await supabase
      .from(WAITLIST_TABLE)
      .update({
        beta_status: 'accepted',
        beta_accessed_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', invitation.waitlist_id);

    if (updateWaitlistError) {
      return { success: false, error: updateWaitlistError.message };
    }

    // Clear beta access cache for this email
    try {
      const { clearBetaAccessCache } = await import('@/hooks/useBetaAccess');
      clearBetaAccessCache(invitation.email);
    } catch (importError) {
      debugWarn('Could not clear beta access cache:', importError);
    }

    return { success: true };
  } catch (error) {
    debugError('Error accepting beta invitation:', error);
    return { success: false, error: 'Failed to accept invitation' };
  }
};

/**
 * Check if user has beta access by email
 */
export const checkBetaAccess = async (email: string): Promise<boolean> => {
  try {
    const { data, error } = await supabase
      .from(WAITLIST_TABLE)
      .select('beta_status')
      .eq('email', email.toLowerCase().trim())
      .eq('beta_status', 'accepted')
      .single();

    if (error) {
      return false;
    }

    return !!data;
  } catch (error) {
    debugError('Error checking beta access:', error);
    return false;
  }
};

/**
 * Get all beta invitations
 */
export const getAllBetaInvitations = async (): Promise<BetaInvitation[]> => {
  try {
    const { data, error } = await supabase
      .from(BETA_INVITATIONS_TABLE)
      .select('*')
      .order('invited_at', { ascending: false });

    if (error) {
      debugError('Error fetching beta invitations:', error);
      return [];
    }

    return data as BetaInvitation[];
  } catch (error) {
    debugError('Error fetching beta invitations:', error);
    return [];
  }
};

/**
 * Manually grant beta access to a user (for testing)
 */
export const grantBetaAccess = async (email: string): Promise<{ success: boolean; error?: string }> => {
  try {
    const normalizedEmail = email.toLowerCase().trim();

    const { error } = await supabase
      .from(WAITLIST_TABLE)
      .upsert({
        email: normalizedEmail,
        beta_status: 'accepted',
        beta_accessed_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'email'
      });

    if (error) {
      return { success: false, error: error.message };
    }

    // Clear beta access cache for this email
    try {
      const { clearBetaAccessCache } = await import('@/hooks/useBetaAccess');
      clearBetaAccessCache(normalizedEmail);
    } catch (importError) {
      debugWarn('Could not clear beta access cache:', importError);
    }

    return { success: true };
  } catch (error) {
    debugError('Error granting beta access:', error);
    return { success: false, error: 'Failed to grant beta access' };
  }
};
