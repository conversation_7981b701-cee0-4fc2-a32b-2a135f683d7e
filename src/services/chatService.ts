/**
 * Service for handling chat interactions with the Layrs API
 */
import { Node, Edge } from '@xyflow/react';
import { prepareNestedStructure, ComponentPayload, ConnectionPayload } from '@/services/assessmentService';
import { getFeedbackLevelPrompt } from '@/types/feedbackTypes';
import { debugLog, debugWarn, debugError } from '@/utils/debugLogger';

/**
 * Interface for chat message
 */
export interface ChatMessage {
  id: string;
  content: string;
  sender: 'user' | 'assistant';
  timestamp: Date;
}

/**
 * Interface for chat response from API
 */
interface ChatResponse {
  response: string;
  error?: string;
}

/**
 * Context-Aware Session Manager for the System Design Agent
 */
class ContextAwareSessionManager {
  private sessions: Map<string, string> = new Map();
  private storagePrefix = 'chat_session';

  constructor() {
    this.init();
  }

  private init() {
    // Load existing sessions from localStorage
    this.loadSessionsFromStorage();
  }

  // Load all chat sessions from localStorage
  private loadSessionsFromStorage() {
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith(this.storagePrefix)) {
        const sessionId = localStorage.getItem(key);
        if (sessionId) {
          // Extract context from storage key: chat_session_question_twitter-design
          const contextKey = key.replace(`${this.storagePrefix}_`, '');
          this.sessions.set(contextKey, sessionId);
        }
      }
    }
  }

  // Generate context-specific session key
  private getContextKey(contextType: string, contextId: string): string {
    return `${contextType}-${contextId}`;
  }

  // Generate session storage key
  private getStorageKey(contextKey: string): string {
    return `${this.storagePrefix}_${contextKey}`;
  }

  // Generate context-aware session ID
  private generateSessionId(contextType: string, contextId: string): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `chat_session_${contextType}_${contextId}_${timestamp}_${random}`;
  }

  // Parse context from session ID (for validation/fallback)
  parseSessionId(sessionId: string): { contextType: string; contextId: string } | null {
    const parts = sessionId.split('_');
    if (parts.length >= 4 && parts[0] === 'chat' && parts[1] === 'session') {
      return {
        contextType: parts[2],
        contextId: parts[3]
      };
    }
    return null;
  }

  // Get or create session for specific context
  getSessionForContext(contextType: string, contextId: string): string {
    const contextKey = this.getContextKey(contextType, contextId);
    const storageKey = this.getStorageKey(contextKey);

    // Check memory cache first
    if (this.sessions.has(contextKey)) {
      return this.sessions.get(contextKey)!;
    }

    // Check localStorage
    const savedSession = localStorage.getItem(storageKey);
    if (savedSession) {
      this.sessions.set(contextKey, savedSession);
      return savedSession;
    }

    // Create new session
    const newSession = this.generateSessionId(contextType, contextId);
    this.sessions.set(contextKey, newSession);
    localStorage.setItem(storageKey, newSession);
    return newSession;
  }

  // Start new session for specific context
  startNewSessionForContext(contextType: string, contextId: string): string {
    const contextKey = this.getContextKey(contextType, contextId);
    const storageKey = this.getStorageKey(contextKey);

    const newSession = this.generateSessionId(contextType, contextId);
    this.sessions.set(contextKey, newSession);
    localStorage.setItem(storageKey, newSession);
    return newSession;
  }

  // Legacy method for backward compatibility
  getCurrentSession(): string {
    // Default to free canvas context for backward compatibility
    return this.getSessionForContext('free', 'free-canvas');
  }

  // Legacy method for backward compatibility
  startNewSession(): string {
    // Default to free canvas context for backward compatibility
    return this.startNewSessionForContext('free', 'free-canvas');
  }

  // Get all sessions for debugging/admin purposes
  getAllSessions(): Map<string, string> {
    return new Map(this.sessions);
  }

  // Clear session for specific context
  clearSessionForContext(contextType: string, contextId: string): void {
    const contextKey = this.getContextKey(contextType, contextId);
    const storageKey = this.getStorageKey(contextKey);

    this.sessions.delete(contextKey);
    localStorage.removeItem(storageKey);
  }
}

// Global session manager instance
const sessionManager = new ContextAwareSessionManager();



/**
 * Valid component subtypes recognized by Layrs Agent
 */
const VALID_SUBTYPES = [
  // Client-side components
  'client', 'mobile_app', 'web_app', 'desktop_app',

  // Network components
  'load_balancer', 'api_gateway', 'cdn', 'firewall', 'proxy',

  // Server components
  'server', 'web_server', 'application_server', 'auth_server',

  // Storage components
  'db', 'sql_db', 'nosql_db', 'document_db', 'graph_db', 'time_series_db',
  'object_storage', 'file_system', 'cache',

  // Messaging components
  'message_queue', 'event_bus', 'stream_processor',

  // Compute components
  'container', 'serverless', 'batch_processor',

  // Monitoring components
  'monitoring', 'logging', 'alerting'
];

/**
 * Transform components to the format expected by Layrs Agent
 */
const transformComponentsForLayrsAgent = (components: ComponentPayload[]): any[] => {
  return components.map(component => {
    // Determine the component subtype
    const subtype = getSubtypeFromComponent(component);

    const result: any = {
      id: component.id,
      type: component.type === 'composite' ? 'composite' : 'primitive',
      subtype: subtype,
      label: component.label || component.id
    };

    // Add enhanced metadata based on component subtype
    result.metadata = enhanceMetadata(component.metadata || {}, subtype, component.type);

    // Add nested components if this is a composite component
    if (component.children) {
      result.components = transformComponentsForLayrsAgent(component.children.components);

      // Add connections between children if available
      if (component.children.connections && component.children.connections.length > 0) {
        result.connections = transformConnectionsForLayrsAgent(component.children.connections);
      }
    }

    return result;
  });
};

/**
 * Transform connections to the format expected by Layrs Agent
 */
const transformConnectionsForLayrsAgent = (connections: ConnectionPayload[]): any[] => {
  return connections.map((connection, index) => ({
    id: connection.connectionNumber || `conn-${index}`,
    start: connection.source,
    end: connection.target,
    // Use connectionNumber as the label directly
    label: connection.connectionNumber || `${index + 1}`
  }));
};

/**
 * Get the subtype of a component based on its type
 */
const getSubtypeFromComponent = (component: ComponentPayload): string => {
  // If the component already has a subtype in metadata, use it
  if (component.metadata?.subtype) {
    const subtype = component.metadata.subtype as string;
    // Validate that it's a recognized subtype
    if (VALID_SUBTYPES.includes(subtype.toLowerCase())) {
      return subtype.toLowerCase();
    }
    // If not valid, we'll infer from the type
  }

  // Otherwise, infer from the type
  const typeMapping: Record<string, string> = {
    'database': 'sql_db',
    'nosql': 'nosql_db',
    'mongodb': 'nosql_db',
    'redis': 'cache',
    'memcached': 'cache',
    'server': 'application_server',
    'web': 'web_server',
    'client': 'web_app',
    'mobile': 'mobile_app',
    'desktop': 'desktop_app',
    'load_balancer': 'load_balancer',
    'api_gateway': 'api_gateway',
    'cdn': 'cdn',
    'queue': 'message_queue',
    'kafka': 'message_queue',
    'rabbitmq': 'message_queue',
    'eventbus': 'event_bus',
    'lambda': 'serverless',
    'function': 'serverless',
    'container': 'container',
    'docker': 'container',
    'kubernetes': 'container',
    'monitoring': 'monitoring',
    'logging': 'logging',
    'alerting': 'alerting',
    'composite': 'composite'
  };

  // Try to match the type to a known mapping
  const lowerType = component.type.toLowerCase();
  for (const [key, value] of Object.entries(typeMapping)) {
    if (lowerType.includes(key)) {
      return value;
    }
  }

  // Default fallback - use server as a safe default
  debugWarn(`Unknown component type: ${component.type}, defaulting to 'server'`);
  return 'server';
};

/**
 * Enhance metadata based on component subtype
 */
const enhanceMetadata = (
  originalMetadata: Record<string, any>,
  subtype: string,
  _componentType: string
): Record<string, any> => {
  // Create a copy of the original metadata
  const metadata = { ...originalMetadata };

  // Add subtype-specific metadata if not already present
  switch (subtype) {
    case 'sql_db':
    case 'nosql_db':
    case 'db':
      if (!metadata.purpose) {
        metadata.purpose = `stores ${subtype === 'sql_db' ? 'relational' : 'application'} data`;
      }
      break;

    case 'cache':
      if (!metadata.purpose) {
        metadata.purpose = "caches frequently accessed data";
      }
      break;

    case 'server':
    case 'web_server':
    case 'application_server':
      if (!metadata.logic) {
        metadata.logic = `handles ${subtype === 'web_server' ? 'HTTP requests' : 'business logic'}`;
      }
      break;
  }

  return metadata;
};

/**
 * Prepare design data for the chat API
 * @param nodes The nodes in the design
 * @param edges The edges in the design
 * @param userJourneys The user journeys text
 * @param assumptions The assumptions text
 * @param constraints The constraints text
 * @param currentQuestion The current question (optional)
 * @param currentCourse The current course (optional)
 * @returns The formatted design data
 */
const prepareDesignData = (
  nodes: Node[],
  edges: Edge[],
  userJourneys: any,
  assumptions: any,
  constraints: any,
  currentQuestion?: any,
  currentCourse?: any
) => {
  // Extract components and connections from the nodes and edges
  debugLog("prepareDesignData - Input nodes and edges:", {
    nodeCount: nodes.length,
    edgeCount: edges.length,
    nodes: nodes,
    edges: edges
  });

  const { components, connections } = prepareNestedStructure(nodes, edges);

  // Ensure we have valid strings for text fields
  const safeUserJourneys = typeof userJourneys === 'string' ? userJourneys :
    (userJourneys ? String(userJourneys) : '');

  debugLog("prepareDesignData - Processed components and connections:", {
    componentCount: components.length,
    connectionCount: connections.length,
    components: components,
    connections: connections
  });

  debugLog("prepareDesignData - User journey processing:", {
    originalUserJourneys: userJourneys,
    safeUserJourneys: safeUserJourneys,
    userJourneysLength: safeUserJourneys.length,
    userJourneysType: typeof userJourneys
  });

  // Format the design data for the API
  const designData = {
    // Determine the problem description based on context
    problem: getProblemDescription(currentQuestion, currentCourse),
    system_name: getSystemName(currentQuestion, currentCourse),
    components: transformComponentsForLayrsAgent(components),
    connections: transformConnectionsForLayrsAgent(connections),
    core_user_journey: safeUserJourneys,
    // Use question requirements as functional requirements if available,
    // otherwise use course-derived requirements, otherwise use assumptions
    functional_requirements: getFunctionalRequirements(currentQuestion, currentCourse, assumptions),
    // Use question constraints as non-functional requirements if available,
    // otherwise use course-derived constraints, otherwise use constraints
    non_functional_requirements: getNonFunctionalRequirements(currentQuestion, currentCourse, constraints)
  };

  debugLog("Design data being sent to agent:", designData);

  return designData;
};

/**
 * Get the problem description based on current context
 */
const getProblemDescription = (currentQuestion?: any, currentCourse?: any): string => {
  // If we're in a guided course, use the course title
  if (currentCourse && currentCourse.title) {
    return `${currentCourse.title} Course`;
  }

  // If we have a question, use the question title and description
  if (currentQuestion && currentQuestion.title && currentQuestion.description) {
    return `${currentQuestion.title}: ${currentQuestion.description}`;
  }

  // If we only have a question title, use it
  if (currentQuestion && currentQuestion.title) {
    return `${currentQuestion.title} Problem`;
  }

  // Default fallback
  return "System Design";
};

/**
 * Get the system name based on current context
 */
const getSystemName = (currentQuestion?: any, currentCourse?: any): string => {
  // If we're in a guided course, use the course title
  if (currentCourse && currentCourse.title) {
    return currentCourse.title;
  }

  // If we have a question, use the question title
  if (currentQuestion && currentQuestion.title) {
    return currentQuestion.title;
  }

  // Default fallback
  return "User Design";
};

/**
 * Get functional requirements based on current context
 */
const getFunctionalRequirements = (currentQuestion?: any, currentCourse?: any, assumptions?: any): string[] => {
  debugLog("getFunctionalRequirements - Input:", {
    currentQuestion: currentQuestion,
    hasFR: !!currentQuestion?.functional_requirements,
    hasRequirements: !!currentQuestion?.requirements,
    functional_requirements: currentQuestion?.functional_requirements,
    requirements: currentQuestion?.requirements,
    currentCourse: currentCourse,
    assumptions: assumptions
  });

  // Priority 1: Use explicit functional_requirements if available
  if (currentQuestion?.functional_requirements && currentQuestion.functional_requirements.length > 0) {
    const formatted = formatRequirements(currentQuestion.functional_requirements);
    debugLog("getFunctionalRequirements - Using explicit FR column:", formatted);
    return formatted;
  }

  // Priority 2: Fall back to legacy requirements field
  if (currentQuestion?.requirements) {
    const formatted = formatRequirements(currentQuestion.requirements);
    debugLog("getFunctionalRequirements - Using legacy requirements field:", formatted);
    return formatted;
  }

  // Priority 3: If we're in a guided course, derive requirements from course structure
  if (currentCourse) {
    const derived = deriveFunctionalRequirementsFromCourse(currentCourse);
    debugLog("getFunctionalRequirements - Using course-derived requirements:", derived);
    return derived;
  }

  // Priority 4: Fall back to user-entered assumptions
  const fallback = formatRequirements(assumptions);
  debugLog("getFunctionalRequirements - Using assumptions fallback:", fallback);
  return fallback;
};

/**
 * Get non-functional requirements based on current context
 */
const getNonFunctionalRequirements = (currentQuestion?: any, currentCourse?: any, constraints?: any): string[] => {
  debugLog("getNonFunctionalRequirements - Input:", {
    currentQuestion: currentQuestion,
    hasNFR: !!currentQuestion?.non_functional_requirements,
    hasConstraints: !!currentQuestion?.constraints,
    non_functional_requirements: currentQuestion?.non_functional_requirements,
    constraints: currentQuestion?.constraints,
    currentCourse: currentCourse,
    userConstraints: constraints
  });

  // Priority 1: Use explicit non_functional_requirements if available
  if (currentQuestion?.non_functional_requirements && currentQuestion.non_functional_requirements.length > 0) {
    const formatted = formatRequirements(currentQuestion.non_functional_requirements);
    debugLog("getNonFunctionalRequirements - Using explicit NFR column:", formatted);
    return formatted;
  }

  // Priority 2: Fall back to legacy constraints field (but this now contains technical constraints)
  if (currentQuestion?.constraints) {
    const formatted = formatRequirements(currentQuestion.constraints);
    debugLog("getNonFunctionalRequirements - Using legacy constraints field:", formatted);
    return formatted;
  }

  // Priority 3: If we're in a guided course, derive constraints from course structure
  if (currentCourse) {
    const derived = deriveNonFunctionalRequirementsFromCourse(currentCourse);
    debugLog("getNonFunctionalRequirements - Using course-derived constraints:", derived);
    return derived;
  }

  // Priority 4: Fall back to user-entered constraints
  const fallback = formatRequirements(constraints);
  debugLog("getNonFunctionalRequirements - Using user constraints fallback:", fallback);
  return fallback;
};

/**
 * Derive functional requirements from course structure
 */
const deriveFunctionalRequirementsFromCourse = (course: any): string[] => {
  const requirements: string[] = [];

  // Add course description as a primary requirement
  if (course.description) {
    requirements.push(course.description);
  }

  // Extract key functional requirements based on course type
  const courseTitle = course.title?.toLowerCase() || '';

  if (courseTitle.includes('instagram')) {
    requirements.push(
      'Users should be able to upload and share photos and videos',
      'Users should be able to follow other users and see their content in a feed',
      'Users should be able to like, comment, and share posts',
      'The system should support user authentication and profiles',
      'The system should handle media processing and storage'
    );
  } else if (courseTitle.includes('twitter')) {
    requirements.push(
      'Users should be able to post short messages (tweets)',
      'Users should be able to follow other users and see their tweets in a timeline',
      'Users should be able to like, retweet, and reply to tweets',
      'The system should support real-time tweet delivery',
      'The system should provide search functionality for tweets and users'
    );
  } else if (courseTitle.includes('url shortener')) {
    requirements.push(
      'Users should be able to input a long URL and get a shortened URL',
      'When users access the shortened URL, they should be redirected to the original URL',
      'The system should track click analytics for shortened URLs',
      'Users should be able to customize their shortened URLs',
      'The system should handle URL expiration'
    );
  } else if (courseTitle.includes('e-commerce') || courseTitle.includes('ecommerce')) {
    requirements.push(
      'Users should be able to browse and search for products',
      'Users should be able to add products to cart and checkout',
      'The system should handle inventory management',
      'The system should process payments securely',
      'Users should receive order confirmations and shipping updates'
    );
  } else {
    // Generic system design requirements
    requirements.push(
      'The system should handle user authentication and authorization',
      'The system should provide a responsive user interface',
      'The system should store and retrieve data efficiently',
      'The system should handle concurrent user requests'
    );
  }

  return requirements;
};

/**
 * Derive non-functional requirements from course structure
 */
const deriveNonFunctionalRequirementsFromCourse = (course: any): string[] => {
  const constraints: string[] = [];

  // Add difficulty-based constraints
  const difficulty = course.difficulty?.toLowerCase() || '';

  if (difficulty === 'beginner') {
    constraints.push(
      'The system should be simple and easy to understand',
      'The system should handle moderate traffic loads',
      'The system should prioritize reliability over complex optimizations'
    );
  } else if (difficulty === 'intermediate') {
    constraints.push(
      'The system should be scalable to handle increased traffic',
      'The system should implement caching for improved performance',
      'The system should handle failure scenarios gracefully'
    );
  } else if (difficulty === 'advanced') {
    constraints.push(
      'The system should be highly scalable and handle millions of users',
      'The system should have sub-second response times',
      'The system should be highly available with minimal downtime',
      'The system should implement advanced optimization techniques'
    );
  }

  // Add course-specific constraints
  const courseTitle = course.title?.toLowerCase() || '';

  if (courseTitle.includes('instagram') || courseTitle.includes('twitter')) {
    constraints.push(
      'The system should handle high read-to-write ratios',
      'The system should support real-time content delivery',
      'The system should handle traffic spikes during major events'
    );
  } else if (courseTitle.includes('url shortener')) {
    constraints.push(
      'URL redirection should happen with minimal latency',
      'The system should be highly available as downtime affects all shortened links',
      'Shortened links should not be predictable for security'
    );
  } else if (courseTitle.includes('e-commerce') || courseTitle.includes('ecommerce')) {
    constraints.push(
      'The system should handle high traffic during sales events',
      'Payment processing should be secure and compliant',
      'Product data should be consistent across all services'
    );
  }

  // Add general scalability constraints
  constraints.push(
    'The system should be horizontally scalable',
    'The system should implement proper monitoring and alerting',
    'The system should have disaster recovery capabilities'
  );

  return constraints;
};

/**
 * Format requirements from text into an array of distinct requirements
 */
const formatRequirements = (requirementsText: any): string[] => {
  // Handle array input
  if (Array.isArray(requirementsText)) {
    return requirementsText.map(item =>
      typeof item === 'string' ? item.trim() : String(item)
    ).filter(item => item.length > 0);
  }

  // Handle string input
  if (typeof requirementsText === 'string') {
    if (requirementsText.trim() === '') {
      return [];
    }

    // Split by common requirement delimiters
    const requirements = requirementsText
      // Split by numbered lists (1., 2., etc.)
      .split(/\n\s*\d+\.\s+/)
      // Split by bullet points
      .flatMap(item => item.split(/\n\s*[\-\*•]\s+/))
      // Split by double newlines
      .flatMap(item => item.split(/\n\n+/))
      // Clean up and filter empty items
      .map(item => item.trim())
      .filter(item => item.length > 0);

    return requirements;
  }

  // Handle object or other types
  if (requirementsText && typeof requirementsText === 'object') {
    try {
      return [JSON.stringify(requirementsText)];
    } catch (e) {
      debugWarn('Could not stringify requirements object:', e);
      return [];
    }
  }

  // Handle null, undefined, or other types
  return [];
};

/**
 * Send a message to the Layrs chat API
 * @param message The user's message
 * @param nodes The nodes in the design (optional)
 * @param edges The edges in the design (optional)
 * @param userJourneys The user journeys text (optional)
 * @param assumptions The assumptions text (optional)
 * @param constraints The constraints text (optional)
 * @param currentQuestion The current question (optional)
 * @param currentCourse The current course (optional)
 * @param onStream Callback function to handle streaming responses (optional)
 * @param feedbackLevel The feedback level for response style (optional)
 * @param contextType The context type (question, course, free) (optional)
 * @param contextId The context ID (optional)
 * @param userId The user ID (optional)
 * @returns The assistant's response
 */
export const sendChatMessage = async (
  message: string,
  nodes?: Node[],
  edges?: Edge[],
  userJourneys?: string,
  assumptions?: string,
  constraints?: string,
  currentQuestion?: any,
  currentCourse?: any,
  onStream?: (chunk: string, done: boolean) => void,
  feedbackLevel?: string,
  contextType?: string,
  contextId?: string,
  userId?: string
): Promise<string> => {
  try {
    // Determine context if not provided
    const effectiveContextType = contextType || (currentQuestion ? 'question' : currentCourse ? 'course' : 'free');
    const effectiveContextId = contextId || (currentQuestion?.id || currentCourse?.id || 'free-canvas');

    // Get context-specific session
    const sessionId = sessionManager.getSessionForContext(effectiveContextType, effectiveContextId);

    debugLog("Nodes and edges being sent to agent:", nodes, edges);

    // Prepare design data if nodes/edges are provided (using the comprehensive format)
    const designData = prepareDesignData(
      nodes || [],
      edges || [],
      userJourneys,
      assumptions,
      constraints,
      currentQuestion,
      currentCourse
    );

    debugLog("Design data being sent to agent:", designData);

    debugLog("Sending message to System Design Agent:", {
      message,
      sessionId,
      feedbackLevel: feedbackLevel || 'educational',
      hasDesignData: !!designData,
      designDataStructure: designData ? {
        problem: designData.problem,
        system_name: designData.system_name,
        components_count: designData.components?.length || 0,
        connections_count: designData.connections?.length || 0,
        has_functional_requirements: !!designData.functional_requirements?.length,
        functional_requirements: designData.functional_requirements,
        has_non_functional_requirements: !!designData.non_functional_requirements?.length,
        non_functional_requirements: designData.non_functional_requirements,
        has_user_journey: !!designData.core_user_journey,
        core_user_journey: designData.core_user_journey
      } : null
    });

    // Prepare request payload with context information
    const requestPayload = {
      message,
      design: designData,
      session_id: sessionId,
      context_type: effectiveContextType,
      context_id: effectiveContextId,
      feedback_level: feedbackLevel || 'educational',
      feedback_prompt: getFeedbackLevelPrompt(feedbackLevel as any || 'educational'),
      user_id: userId
    };

    // Log the request for debugging
    debugLog('🚀 Chat API Request:', {
      session_id: sessionId,
      context_type: effectiveContextType,
      context_id: effectiveContextId,
      message: message.substring(0, 100) + '...',
      feedback_level: feedbackLevel || 'educational',
      user_id: userId
    });

    // Make the API call to local agent (matching systemDesignAgentService)
    const response = await fetch('http://localhost:9000/api/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestPayload)
    });

    debugLog("Chat response status:", response.status);
    debugLog("Chat response headers:", Object.fromEntries([...response.headers.entries()]));

    if (!response.ok) {
      const errorText = await response.text();
      debugError("Chat response error:", errorText);
      throw new Error(`Agent request failed with status: ${response.status} - ${errorText}`);
    }

    const responseText = await response.text();
    debugLog("Raw chat response:", responseText);

    let data: ChatResponse;
    try {
      data = JSON.parse(responseText) as ChatResponse;
    } catch (parseError) {
      debugError("Failed to parse chat response as JSON:", parseError);
      debugLog("Response was:", responseText);
      // If it's not JSON, treat the raw text as the response
      return responseText || "I'm sorry, I couldn't process your request.";
    }

    debugLog("Parsed chat response:", data);

    if (data.error) {
      throw new Error(data.error);
    }

    const finalResponse = data.response || responseText || "I'm sorry, I couldn't process your request.";
    debugLog("Final chat response:", finalResponse);

    return finalResponse;
  } catch (error) {
    debugError("Error during agent chat request:", error);
    return "I'm sorry, I couldn't connect to the system design agent.";
  }
};

/**
 * Check if the agent is available
 */
export const checkAgentAvailability = async (): Promise<boolean> => {
  try {
    const response = await fetch('https://api.layrs.me/api/test');
    return response.ok;
  } catch (error) {
    return false;
  }
};

/**
 * Start a new design session
 */
export const startNewDesignSession = (): string => {
  return sessionManager.startNewSession();
};

/**
 * Get session for specific context
 */
export const getSessionForContext = (contextType: string, contextId: string): string => {
  return sessionManager.getSessionForContext(contextType, contextId);
};

/**
 * Start new session for specific context
 */
export const startNewSessionForContext = (contextType: string, contextId: string): string => {
  return sessionManager.startNewSessionForContext(contextType, contextId);
};

/**
 * Clear session for specific context
 */
export const clearSessionForContext = (contextType: string, contextId: string): void => {
  return sessionManager.clearSessionForContext(contextType, contextId);
};

/**
 * Get all sessions (for debugging)
 */
export const getAllSessions = (): Map<string, string> => {
  return sessionManager.getAllSessions();
};

/**
 * Test function to demonstrate context-aware sessions
 */
export const testContextAwareSessions = () => {
  debugLog('🧪 Testing Context-Aware Sessions');

  // Test different contexts
  const freeCanvasSession = getSessionForContext('free', 'free-canvas');
  const urlShortenerSession = getSessionForContext('question', '1');
  const socialMediaSession = getSessionForContext('question', '2');
  const courseSession = getSessionForContext('course', 'system-design-101');

  debugLog('📋 Session IDs by Context:');
  debugLog('  Free Canvas:', freeCanvasSession);
  debugLog('  URL Shortener Question:', urlShortenerSession);
  debugLog('  Social Media Question:', socialMediaSession);
  debugLog('  Course:', courseSession);

  // Verify they're different
  const allUnique = new Set([freeCanvasSession, urlShortenerSession, socialMediaSession, courseSession]).size === 4;
  debugLog('✅ All sessions unique:', allUnique);

  // Test session persistence
  const freeCanvasSession2 = getSessionForContext('free', 'free-canvas');
  const persistent = freeCanvasSession === freeCanvasSession2;
  debugLog('💾 Session persistence works:', persistent);

  return {
    freeCanvasSession,
    urlShortenerSession,
    socialMediaSession,
    courseSession,
    allUnique,
    persistent
  };
};

// Make debug functions available globally for testing
if (typeof window !== 'undefined') {
  (window as any).layrsDebug = {
    testContextAwareSessions,
    getAllSessions,
    getSessionForContext,
    startNewSessionForContext,
    clearSessionForContext
  };
}
