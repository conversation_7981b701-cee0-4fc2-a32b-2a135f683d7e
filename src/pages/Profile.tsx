import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useBetaAccess } from '@/hooks/useBetaAccess';
import { useSubscription } from '@/hooks/useSubscription';
import { subscriptionService } from '@/services/subscriptionService';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { useNavigate } from 'react-router-dom';
import Header from '@/components/Header';

import { Badge } from '@/components/ui/badge';
import { CheckCircle, AlertCircle, ExternalLink, Star, CreditCard, Zap, Crown } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { getEmailProviderUrl } from '@/lib/emailUtils';
import {
  Dialog,
  DialogTrigger,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose
} from '@/components/ui/dialog';

const Profile: React.FC = () => {
  const { user, signOut, isEmailVerified, sendVerificationEmail } = useAuth();
  const { hasBetaAccess } = useBetaAccess();
  const { subscription, planInfo, credits, loading, canUpgrade, isProUser, refetch } = useSubscription();
  const navigate = useNavigate();

  const [showUpgradeModal, setShowUpgradeModal] = React.useState(false);
  const [upgrading, setUpgrading] = React.useState(false);

  const handleSignOut = async () => {
    await signOut();
    navigate('/login');
  };

  const handleStartAssessment = () => {
    navigate('/canvas');
  };

  const handleSendVerificationEmail = async () => {
    if (user?.email) {
      await sendVerificationEmail(user.email);
    }
  };

  const handleUpgradeClick = () => {
    setShowUpgradeModal(true);
  };

  const handleSubscribeToPro = async () => {
    if (!user?.email) return;

    setUpgrading(true);
    try {
      // Get current domain for return URL (works with ngrok and localhost)
      const currentOrigin = window.location.origin;
      const returnUrl = `${currentOrigin}/profile?upgrade=success`;

      const response = await subscriptionService.createSubscription({
        plan_type: 'pro',
        customer_data: {
          name: user.user_metadata?.full_name || user.email,
          email: user.email,
        },
        return_url: returnUrl,
      });

      if (response.success && response.payment_link) {
        // Redirect to DodoPayments checkout
        window.location.href = response.payment_link;
      } else {
        console.error('Subscription creation failed:', response.error);
        alert('Failed to create subscription. Please try again.');
      }
    } catch (error) {
      console.error('Error creating subscription:', error);
      alert('An error occurred. Please try again.');
    } finally {
      setUpgrading(false);
      setShowUpgradeModal(false);
    }
  };

  return (
    <div className="flex flex-col min-h-screen">
      <Header onStartAssessment={handleStartAssessment} />

      <div className="flex-1 p-6 flex flex-col items-center">
        <div className="w-full max-w-2xl space-y-6">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle className="text-2xl">Profile</CardTitle>
                  <CardDescription>
                    Your account information
                  </CardDescription>
                </div>
                {isEmailVerified ? (
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 flex items-center gap-1">
                    <CheckCircle className="h-3 w-3" />
                    Verified
                  </Badge>
                ) : (
                  <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200 flex items-center gap-1">
                    <AlertCircle className="h-3 w-3" />
                    Unverified
                  </Badge>
                )}
                {hasBetaAccess && (
                  <Badge className="bg-gradient-to-r from-purple-500 to-blue-500 text-white border-0 flex items-center gap-1">
                    <Star className="h-3 w-3" />
                    Beta Access
                  </Badge>
                )}
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {!isEmailVerified && (
                <Alert variant="destructive" className="bg-yellow-50 border-yellow-200 text-yellow-800">
                  <AlertCircle className="h-4 w-4 text-yellow-600" />
                  <AlertDescription className="text-yellow-800">
                    <div className="flex flex-col space-y-2">
                      <p>
                        Your email address is not verified. Please check your inbox or{' '}
                        <Button
                          variant="link"
                          className="p-0 h-auto text-yellow-800 underline font-medium"
                          onClick={handleSendVerificationEmail}
                        >
                          click here to resend the verification email
                        </Button>.
                      </p>
                      {user?.email && (
                        <Button
                          size="sm"
                          variant="outline"
                          className="bg-white border-yellow-300 text-yellow-800 hover:bg-yellow-50 w-fit flex items-center gap-1"
                          onClick={() => window.open(getEmailProviderUrl(user.email), '_blank')}
                        >
                          <ExternalLink className="h-3 w-3" />
                          Open Email Provider
                        </Button>
                      )}
                    </div>
                  </AlertDescription>
                </Alert>
              )}

              <div className="space-y-1">
                <p className="text-sm font-medium">Email</p>
                <div className="flex items-center">
                  <p className="text-sm text-gray-500">{user?.email}</p>
                  {isEmailVerified && (
                    <CheckCircle className="h-4 w-4 text-green-500 ml-2" />
                  )}
                </div>
              </div>

              <div className="space-y-1">
                <p className="text-sm font-medium">Last Sign In</p>
                <p className="text-sm text-gray-500">
                  {user?.last_sign_in_at
                    ? new Date(user.last_sign_in_at).toLocaleString()
                    : 'N/A'}
                </p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium">Account Created</p>
                <p className="text-sm text-gray-500">
                  {user?.created_at
                    ? new Date(user.created_at).toLocaleString()
                    : 'N/A'}
                </p>
              </div>
            </CardContent>
            <CardFooter>
              <Button
                variant="destructive"
                onClick={handleSignOut}
                className="w-full"
              >
                Sign out
              </Button>
            </CardFooter>
          </Card>
        </div>
        {/* Subscription Management Section */}
        <div className="w-full max-w-2xl mt-6">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle className="text-2xl flex items-center gap-2">
                    Subscription
                    {isProUser && <Crown className="h-5 w-5 text-yellow-500" />}
                  </CardTitle>
                  <CardDescription>
                    Manage your subscription and view your current plan.
                  </CardDescription>
                </div>
                {planInfo && (
                  <Badge 
                    variant={isProUser ? "default" : "outline"} 
                    className={isProUser ? "bg-gradient-to-r from-purple-500 to-blue-500 text-white" : ""}
                  >
                    {planInfo.name}
                  </Badge>
                )}
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                </div>
              ) : (
                <>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-1">
                      <p className="text-sm font-medium">Current Plan</p>
                      <p className="text-sm text-gray-500">{planInfo?.name || 'Loading...'}</p>
                    </div>
                    <div className="space-y-1">
                      <p className="text-sm font-medium flex items-center gap-1">
                        <Zap className="h-3 w-3" />
                        Credits Available
                      </p>
                      <p className="text-sm text-gray-500">{credits} credits</p>
                    </div>
                  </div>
                  
                  {planInfo && (
                    <div className="space-y-1">
                      <p className="text-sm font-medium">Plan Details</p>
                      <div className="text-sm text-gray-500 space-y-1">
                        <p>• {planInfo.credits} credits per month</p>
                        <p>• {subscriptionService.formatPrice(planInfo.price)} {planInfo.period}</p>
                        {isProUser && <p>• Priority support</p>}
                        {isProUser && <p>• Advanced features</p>}
                      </div>
                    </div>
                  )}

                  {subscription?.next_billing_date && (
                    <div className="space-y-1">
                      <p className="text-sm font-medium">Next Billing</p>
                      <p className="text-sm text-gray-500">
                        {new Date(subscription.next_billing_date).toLocaleDateString()}
                      </p>
                    </div>
                  )}
                </>
              )}
            </CardContent>
            <CardFooter>
              {canUpgrade ? (
                <Button 
                  className="w-full bg-gradient-to-r from-purple-500 to-blue-500 text-white" 
                  onClick={handleUpgradeClick}
                  disabled={loading}
                >
                  <Crown className="h-4 w-4 mr-2" />
                  Upgrade to Pro
                </Button>
              ) : isProUser ? (
                <Button variant="outline" className="w-full" disabled>
                  <CreditCard className="h-4 w-4 mr-2" />
                  Manage Billing (Coming Soon)
                </Button>
              ) : (
                <Button variant="outline" className="w-full" disabled>
                  Loading...
                </Button>
              )}
            </CardFooter>
          </Card>
        </div>
        {/* Upgrade Modal */}
        <Dialog open={showUpgradeModal} onOpenChange={setShowUpgradeModal}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Crown className="h-5 w-5 text-yellow-500" />
                Upgrade to Pro
              </DialogTitle>
              <DialogDescription>
                Unlock more credits and advanced features with Pro plan.
              </DialogDescription>
            </DialogHeader>
            
            <div className="space-y-4">
              <Card className="border-2 border-blue-500">
                <CardHeader className="pb-3">
                  <div className="flex justify-between items-center">
                    <CardTitle className="text-lg">Pro Plan</CardTitle>
                    <Badge className="bg-gradient-to-r from-purple-500 to-blue-500 text-white">
                      Popular
                    </Badge>
                  </div>
                  <div className="flex items-end gap-1">
                    <span className="text-3xl font-bold">$9.99</span>
                    <span className="text-base text-gray-500">/month</span>
                  </div>
                </CardHeader>
                <CardContent className="pb-3">
                  <ul className="space-y-2 text-sm">
                    <li className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      100 credits per month
                    </li>
                    <li className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      Priority support
                    </li>
                    <li className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      Advanced features
                    </li>
                    <li className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      Cancel anytime
                    </li>
                  </ul>
                </CardContent>
              </Card>
            </div>

            <DialogFooter className="flex gap-2">
              <DialogClose asChild>
                <Button variant="outline" className="flex-1">
                  Maybe Later
                </Button>
              </DialogClose>
              <Button 
                onClick={handleSubscribeToPro}
                disabled={upgrading}
                className="flex-1 bg-gradient-to-r from-purple-500 to-blue-500 text-white"
              >
                {upgrading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Processing...
                  </>
                ) : (
                  <>
                    <Crown className="h-4 w-4 mr-2" />
                    Subscribe Now
                  </>
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
};

export default Profile;
