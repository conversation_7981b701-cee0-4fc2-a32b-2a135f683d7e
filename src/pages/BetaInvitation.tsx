
import React, { useState, useEffect } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from 'sonner';
import { Loader2, CheckCircle, XCircle, Mail } from 'lucide-react';
import { getBetaInvitationByToken, acceptBetaInvitation, type BetaInvitation } from '@/services/betaService';

const BetaInvitationPage: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [accepting, setAccepting] = useState(false);
  const [invitation, setInvitation] = useState<BetaInvitation | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [accepted, setAccepted] = useState(false);

  const token = searchParams.get('token');

  useEffect(() => {
    const validateInvitation = async () => {
      if (!token) {
        setError('Invalid invitation link - no token provided');
        setLoading(false);
        return;
      }

      try {
        const { success, invitation, error } = await getBetaInvitationByToken(token);
        
        if (!success || !invitation) {
          setError(error || 'Invalid or expired invitation');
        } else {
          setInvitation(invitation);
          // Check if already accepted
          if (invitation.status === 'accepted') {
            setAccepted(true);
          }
        }
      } catch (err) {
        console.error('Error validating invitation:', err);
        setError('Failed to validate invitation');
      } finally {
        setLoading(false);
      }
    };

    validateInvitation();
  }, [token]);

  const handleAcceptInvitation = async () => {
    if (!token) return;

    setAccepting(true);

    try {
      const { success, error } = await acceptBetaInvitation(token);

      if (!success) {
        toast.error('Failed to accept invitation', {
          description: error || 'An unexpected error occurred.'
        });
        return;
      }

      setAccepted(true);
      toast.success('Welcome to Layrs Beta!', {
        description: 'You now have access to beta features.'
      });

      // Redirect to login/register after a short delay
      setTimeout(() => {
        navigate('/login?beta=true');
      }, 2000);

    } catch (error) {
      console.error('Error accepting invitation:', error);
      toast.error('Failed to accept invitation', {
        description: 'An unexpected error occurred.'
      });
    } finally {
      setAccepting(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-[#1e1b4b] via-[#2e1065] to-[#1e1b4b]">
        <Card className="w-full max-w-md">
          <CardContent className="flex items-center justify-center py-12">
            <Loader2 className="w-8 h-8 animate-spin text-blue-500" />
            <span className="ml-2">Validating invitation...</span>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-[#1e1b4b] via-[#2e1065] to-[#1e1b4b]">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <XCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
            <CardTitle className="text-red-600">Invalid Invitation</CardTitle>
            <CardDescription>{error}</CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={() => navigate('/')} 
              className="w-full"
              variant="outline"
            >
              Go to Homepage
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (accepted) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-[#1e1b4b] via-[#2e1065] to-[#1e1b4b]">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
            <CardTitle className="text-green-600">Welcome to Layrs Beta!</CardTitle>
            <CardDescription>
              You've successfully joined the beta program. You'll be redirected to login shortly.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={() => navigate('/login?beta=true')} 
              className="w-full bg-blue-600 hover:bg-blue-700"
            >
              Continue to Login
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-[#1e1b4b] via-[#2e1065] to-[#1e1b4b]">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <Mail className="w-16 h-16 text-blue-500 mx-auto mb-4" />
          <CardTitle>You're Invited to Layrs Beta!</CardTitle>
          <CardDescription>
            You've been selected to join our exclusive beta program.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {invitation && (
            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="font-semibold text-blue-900 mb-2">Invitation Details</h3>
              <p className="text-sm text-blue-700">
                <strong>Email:</strong> {invitation.email}
              </p>
              <p className="text-sm text-blue-700">
                <strong>Expires:</strong> {new Date(invitation.expires_at).toLocaleDateString()}
              </p>
            </div>
          )}
          
          <div className="space-y-2">
            <h4 className="font-semibold">What you'll get:</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Early access to new features</li>
              <li>• Priority support</li>
              <li>• Direct feedback channel to our team</li>
              <li>• Exclusive beta user community</li>
            </ul>
          </div>

          <Button 
            onClick={handleAcceptInvitation}
            disabled={accepting}
            className="w-full bg-blue-600 hover:bg-blue-700"
          >
            {accepting ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Accepting...
              </>
            ) : (
              'Accept Beta Invitation'
            )}
          </Button>

          <Button 
            onClick={() => navigate('/')} 
            variant="outline"
            className="w-full"
          >
            Maybe Later
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};

export default BetaInvitationPage;
