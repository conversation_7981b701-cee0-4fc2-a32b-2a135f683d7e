
import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/contexts/AuthContext';
import { isFeatureEnabled } from '@/config/featureFlags';
import { 
  ArrowRight, 
  Zap, 
  Shield, 
  Layers, 
  Code, 
  Users, 
  Sparkles,
  CheckCircle,
  Star,
  Play,
  ChevronRight,
  Globe,
  Brain,
  Target,
  Clock,
  Award,
  TrendingUp,
  Rocket,
  Eye,
  Lightbulb,
  BarChart3
} from 'lucide-react';

const ModernLandingPage: React.FC = () => {
  const { user } = useAuth();

  const features = [
    {
      icon: <Layers className="h-6 w-6" />,
      title: "Visual System Design",
      description: "Design complex architectures with intuitive drag-and-drop components. Build systems that scale with confidence.",
      benefits: ["Drag & drop interface", "Real-time collaboration", "Auto-save functionality", "Export to multiple formats"]
    },
    {
      icon: <Brain className="h-6 w-6" />,
      title: "AI-Powered Insights", 
      description: "Get intelligent suggestions and real-time feedback on your designs from our advanced AI assistant.",
      benefits: ["Smart pattern recognition", "Performance optimization tips", "Security recommendations", "Best practice validation"]
    },
    {
      icon: <Code className="h-6 w-6" />,
      title: "Code Generation",
      description: "Transform your visual designs into production-ready documentation and implementation guides.",
      benefits: ["Auto-generated docs", "Code snippets", "API specifications", "Deployment guides"]
    },
    {
      icon: <Target className="h-6 w-6" />,
      title: "Interview Preparation",
      description: "Practice with curated system design problems and get detailed feedback to ace your interviews.",
      benefits: ["500+ practice problems", "Mock interview mode", "Performance analytics", "Progress tracking"]
    }
  ];

  const benefits = [
    {
      icon: <TrendingUp className="h-5 w-5 text-green-600" />,
      title: "10x Faster Design Process",
      description: "Complete system designs in minutes, not hours, with our intuitive visual tools and AI assistance."
    },
    {
      icon: <Shield className="h-5 w-5 text-blue-600" />,
      title: "Industry-Standard Patterns",
      description: "Built-in validation ensures your designs follow proven architectural patterns and best practices."
    },
    {
      icon: <Users className="h-5 w-5 text-purple-600" />,
      title: "Team Collaboration",
      description: "Real-time collaboration features let your entire team contribute to system designs seamlessly."
    },
    {
      icon: <Award className="h-5 w-5 text-yellow-600" />,
      title: "Interview Success",
      description: "98% of users report improved confidence and performance in system design interviews."
    },
    {
      icon: <Clock className="h-5 w-5 text-indigo-600" />,
      title: "Save 20+ Hours Weekly",
      description: "Automate documentation, generate code snippets, and eliminate repetitive design tasks."
    },
    {
      icon: <Rocket className="h-5 w-5 text-red-600" />,
      title: "Career Acceleration",
      description: "Master system design skills that are essential for senior engineering roles and promotions."
    }
  ];

  const stats = [
    { number: "50K+", label: "Engineers Trained", subtext: "Worldwide" },
    { number: "500+", label: "Design Problems", subtext: "Curated Library" },
    { number: "98%", label: "Success Rate", subtext: "Interview Pass Rate" },
    { number: "4.9/5", label: "User Rating", subtext: "Trusted by Professionals" }
  ];

  const testimonials = [
    {
      name: "Sarah Chen",
      role: "Senior Engineer at Meta",
      company: "Meta",
      content: "Layrs completely transformed my approach to system design. The AI feedback and visual tools helped me think through complex architectures more systematically. I aced my L6 promotion interview thanks to the skills I developed here.",
      rating: 5,
      avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b647?w=150&h=150&fit=crop&crop=face"
    },
    {
      name: "Michael Rodriguez",
      role: "Tech Lead at Stripe",
      company: "Stripe",
      content: "The collaborative features are game-changing. My team now uses Layrs for all our architecture reviews. The ability to iterate quickly and get instant feedback has improved our design quality significantly.",
      rating: 5,
      avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face"
    },
    {
      name: "Emily Johnson",
      role: "Engineering Director at Netflix",
      company: "Netflix",
      content: "We've integrated Layrs into our engineering onboarding. New hires get up to speed on our architecture 3x faster. The interview prep features are also fantastic for internal promotions.",
      rating: 5,
      avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face"
    }
  ];

  const companies = [
    { name: "Meta", logo: "🔵" },
    { name: "Google", logo: "🔴" },
    { name: "Netflix", logo: "🔺" },
    { name: "Stripe", logo: "🟣" },
    { name: "Uber", logo: "⚫" },
    { name: "Airbnb", logo: "🔶" }
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* Navigation */}
      <nav className="sticky top-0 z-50 bg-white/95 backdrop-blur-lg border-b border-gray-100">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
                <Layers className="h-6 w-6 text-white" />
              </div>
              <span className="text-2xl font-bold text-gray-900">Layrs</span>
            </div>
            
            <div className="hidden md:flex items-center space-x-8">
              <Link to="/questions" className="text-gray-600 hover:text-gray-900 transition-colors font-medium">Problems</Link>
              <a href="#features" className="text-gray-600 hover:text-gray-900 transition-colors font-medium">Features</a>
              <a href="#pricing" className="text-gray-600 hover:text-gray-900 transition-colors font-medium">Pricing</a>
              <a href="#testimonials" className="text-gray-600 hover:text-gray-900 transition-colors font-medium">Reviews</a>
            </div>
            
            <div className="flex items-center space-x-4">
              {user ? (
                <Button asChild className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-semibold shadow-lg hover:shadow-xl transition-all">
                  <Link to={isFeatureEnabled('FREE_CANVAS') ? "/home" : "/questions"}>
                    {isFeatureEnabled('FREE_CANVAS') ? "Open Canvas" : "Browse Questions"}
                  </Link>
                </Button>
              ) : (
                <div className="flex space-x-3">
                  <Button asChild variant="ghost" className="text-gray-600 hover:text-gray-900 font-medium">
                    <Link to="/login">Sign In</Link>
                  </Button>
                  <Button asChild className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-semibold shadow-lg hover:shadow-xl transition-all">
                    <Link to="/register">Start Free Trial</Link>
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="pt-16 pb-24 px-6 bg-gradient-to-b from-gray-50 to-white relative overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(59,130,246,0.1),transparent_50%)]"></div>
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(99,102,241,0.1),transparent_50%)]"></div>
        
        <div className="max-w-7xl mx-auto relative">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left Column - Content */}
            <div className="space-y-8">
              <div className="space-y-4">
                <Badge className="bg-gradient-to-r from-blue-500 to-indigo-500 text-white border-none hover:from-blue-600 hover:to-indigo-600 px-4 py-2 rounded-full font-medium">
                  <Sparkles className="w-4 h-4 mr-2" />
                  Used by 50,000+ Engineers
                </Badge>
                
                <h1 className="text-5xl lg:text-7xl font-bold text-gray-900 leading-tight">
                  Master System Design
                  <span className="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent block mt-2">
                    10x Faster
                  </span>
                </h1>
                
                <p className="text-xl lg:text-2xl text-gray-600 leading-relaxed max-w-2xl">
                  Design scalable architectures with AI-powered visual tools. Practice with real problems, get instant feedback, and ace your next system design interview.
                </p>
              </div>
              
              <div className="flex flex-col sm:flex-row gap-4">
                <Button asChild size="lg" className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 text-lg rounded-xl font-semibold shadow-xl hover:shadow-2xl transition-all group">
                  <Link to={user ? "/home" : "/register"}>
                    Start Free Trial
                    <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
                  </Link>
                </Button>
                <Button asChild variant="outline" size="lg" className="border-2 border-gray-300 text-gray-700 hover:bg-gray-50 px-8 py-4 text-lg rounded-xl font-semibold group">
                  <a href="#demo">
                    <Play className="mr-2 h-5 w-5 group-hover:scale-110 transition-transform" />
                    Watch Demo
                  </a>
                </Button>
              </div>

              {/* Companies */}
              <div className="pt-8">
                <p className="text-sm text-gray-500 mb-4 font-medium">Trusted by engineers at</p>
                <div className="flex items-center space-x-8">
                  {companies.map((company, index) => (
                    <div key={index} className="flex items-center space-x-2 text-gray-400 hover:text-gray-600 transition-colors">
                      <span className="text-2xl">{company.logo}</span>
                      <span className="font-semibold">{company.name}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
            
            {/* Right Column - Hero Visual */}
            <div className="relative">
              <div className="relative bg-white rounded-2xl shadow-2xl border border-gray-200 overflow-hidden">
                <div className="aspect-video bg-gradient-to-br from-blue-50 to-indigo-50 flex items-center justify-center">
                  <div className="text-center space-y-4">
                    <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-2xl flex items-center justify-center mx-auto shadow-lg">
                      <Layers className="h-10 w-10 text-white" />
                    </div>
                    <div className="space-y-2">
                      <h3 className="text-xl font-bold text-gray-900">Interactive System Designer</h3>
                      <p className="text-gray-600">Drag, drop, and design scalable architectures</p>
                    </div>
                  </div>
                </div>
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                  <Button className="bg-white/90 text-gray-900 hover:bg-white shadow-lg">
                    <Play className="mr-2 h-4 w-4" />
                    Try Interactive Demo
                  </Button>
                </div>
              </div>
              
              {/* Floating Elements */}
              <div className="absolute -top-4 -right-4 bg-green-500 text-white px-3 py-1 rounded-full text-sm font-semibold shadow-lg animate-bounce">
                AI Powered
              </div>
              <div className="absolute -bottom-4 -left-4 bg-purple-500 text-white px-3 py-1 rounded-full text-sm font-semibold shadow-lg">
                Real-time Feedback
              </div>
            </div>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8 mt-20 pt-12 border-t border-gray-200">
            {stats.map((stat, index) => (
              <div key={index} className="text-center group">
                <div className="text-4xl lg:text-5xl font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">{stat.number}</div>
                <div className="text-lg font-semibold text-gray-700 mb-1">{stat.label}</div>
                <div className="text-sm text-gray-500">{stat.subtext}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Demo Video Section */}
      <section id="demo" className="py-20 px-6 bg-gray-900 text-white">
        <div className="max-w-6xl mx-auto text-center">
          <div className="space-y-6 mb-12">
            <h2 className="text-4xl lg:text-5xl font-bold">
              See Layrs in Action
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Watch how engineers at top tech companies use Layrs to design scalable systems, prepare for interviews, and accelerate their careers.
            </p>
          </div>
          
          <div className="relative">
            <div className="aspect-video bg-gradient-to-br from-gray-800 to-gray-900 rounded-2xl border border-gray-700 overflow-hidden shadow-2xl">
              <div className="absolute inset-0 flex items-center justify-center">
                <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-full shadow-xl">
                  <Play className="mr-3 h-6 w-6" />
                  Play Demo Video
                </Button>
              </div>
            </div>
            
            {/* Video Placeholder with Play Button */}
            <div className="absolute inset-0 bg-gradient-to-br from-blue-600/20 to-indigo-600/20 rounded-2xl"></div>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 px-6 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
              Why 50,000+ Engineers Choose Layrs
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              From faster design workflows to career advancement, discover the benefits that make Layrs essential for modern engineers.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {benefits.map((benefit, index) => (
              <Card key={index} className="bg-white border border-gray-200 hover:border-blue-200 hover:shadow-lg transition-all duration-300 group">
                <CardHeader className="pb-4">
                  <div className="flex items-center space-x-3 mb-3">
                    <div className="p-2 bg-gray-50 rounded-lg group-hover:bg-blue-50 transition-colors">
                      {benefit.icon}
                    </div>
                    <CardTitle className="text-xl font-semibold text-gray-900">{benefit.title}</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-gray-600 text-base leading-relaxed">
                    {benefit.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 px-6 bg-gray-50">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
              Everything You Need to Excel
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Comprehensive tools and features designed to make you a system design expert.
            </p>
          </div>
          
          <div className="grid lg:grid-cols-2 gap-12">
            {features.map((feature, index) => (
              <Card key={index} className="bg-white border border-gray-200 hover:shadow-xl transition-all duration-300 overflow-hidden group">
                <CardHeader className="pb-6">
                  <div className="flex items-start space-x-4">
                    <div className="p-3 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-xl text-white group-hover:scale-110 transition-transform">
                      {feature.icon}
                    </div>
                    <div className="flex-1">
                      <CardTitle className="text-2xl font-bold text-gray-900 mb-2">{feature.title}</CardTitle>
                      <CardDescription className="text-gray-600 text-lg leading-relaxed">
                        {feature.description}
                      </CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {feature.benefits.map((benefit, idx) => (
                      <div key={idx} className="flex items-center space-x-3">
                        <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0" />
                        <span className="text-gray-700 font-medium">{benefit}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section id="testimonials" className="py-20 px-6 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
              Success Stories from Top Engineers
            </h2>
            <p className="text-xl text-gray-600">
              See how Layrs has transformed careers and accelerated professional growth
            </p>
          </div>
          
          <div className="grid lg:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="bg-white border border-gray-200 hover:shadow-xl transition-all duration-300 group">
                <CardContent className="pt-8">
                  <div className="flex mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                    ))}
                  </div>
                  <blockquote className="text-gray-700 text-base leading-relaxed mb-6 font-medium">
                    "{testimonial.content}"
                  </blockquote>
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center text-white font-bold text-lg">
                      {testimonial.name.charAt(0)}
                    </div>
                    <div>
                      <div className="font-bold text-gray-900">{testimonial.name}</div>
                      <div className="text-sm text-gray-600">{testimonial.role}</div>
                      <div className="text-sm font-semibold text-blue-600">{testimonial.company}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="py-24 px-6 bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 relative overflow-hidden">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(255,255,255,0.1),transparent_50%)]"></div>
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(255,255,255,0.1),transparent_50%)]"></div>
        
        <div className="max-w-4xl mx-auto text-center relative">
          <div className="space-y-8">
            <h2 className="text-4xl lg:text-6xl font-bold text-white leading-tight">
              Ready to Transform Your Career?
            </h2>
            <p className="text-xl lg:text-2xl text-blue-100 leading-relaxed max-w-3xl mx-auto">
              Join 50,000+ engineers who've mastered system design with Layrs. Start your free trial today and see why top tech companies trust our platform.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-6 justify-center pt-8">
              <Button asChild size="lg" className="bg-white text-blue-600 hover:bg-gray-50 px-10 py-4 text-xl rounded-xl font-bold shadow-2xl hover:shadow-3xl transition-all group">
                <Link to={user ? "/home" : "/register"}>
                  Start Free Trial
                  <ArrowRight className="ml-3 h-6 w-6 group-hover:translate-x-1 transition-transform" />
                </Link>
              </Button>
              <Button asChild variant="outline" size="lg" className="border-2 border-white/30 text-white hover:bg-white/10 px-10 py-4 text-xl rounded-xl font-bold backdrop-blur-sm">
                <Link to="/questions">Browse 500+ Problems</Link>
              </Button>
            </div>

            <div className="pt-8 text-blue-100">
              <p className="text-lg font-medium">🎉 Limited Time: Get 30 days free + Premium features</p>
              <p className="text-sm opacity-80">No credit card required • Cancel anytime</p>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-16 px-6 bg-gray-900 text-white">
        <div className="max-w-7xl mx-auto">
          <div className="grid md:grid-cols-4 gap-8 mb-12">
            <div className="col-span-2">
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-xl flex items-center justify-center">
                  <Layers className="h-6 w-6 text-white" />
                </div>
                <span className="text-2xl font-bold">Layrs</span>
              </div>
              <p className="text-gray-400 leading-relaxed max-w-md mb-6">
                Master the unseen architecture of great systems. One layer at a time. Trusted by 50,000+ engineers worldwide.
              </p>
              <div className="flex space-x-4">
                <div className="text-2xl">🐦</div>
                <div className="text-2xl">💼</div>
                <div className="text-2xl">📧</div>
              </div>
            </div>
            
            <div>
              <h3 className="font-bold mb-4 text-lg">Product</h3>
              <div className="space-y-3">
                <Link to="/questions" className="block text-gray-400 hover:text-white transition-colors">System Design Problems</Link>
                <Link to="/home" className="block text-gray-400 hover:text-white transition-colors">Visual Designer</Link>
                <a href="#" className="block text-gray-400 hover:text-white transition-colors">AI Assistant</a>
                <a href="#" className="block text-gray-400 hover:text-white transition-colors">Templates</a>
              </div>
            </div>
            
            <div>
              <h3 className="font-bold mb-4 text-lg">Support</h3>
              <div className="space-y-3">
                <a href="#" className="block text-gray-400 hover:text-white transition-colors">Help Center</a>
                <a href="#" className="block text-gray-400 hover:text-white transition-colors">Contact Support</a>
                <a href="#" className="block text-gray-400 hover:text-white transition-colors">Status Page</a>
                <a href="#" className="block text-gray-400 hover:text-white transition-colors">Privacy Policy</a>
              </div>
            </div>
          </div>
          
          <div className="border-t border-gray-800 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400">&copy; 2024 Layrs. All rights reserved.</p>
            <div className="flex items-center space-x-6 mt-4 md:mt-0">
              <span className="text-gray-400">Made with ❤️ for engineers</span>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default ModernLandingPage;
