import React, { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from 'sonner';
import { Loader2, Mail, Users, UserCheck } from 'lucide-react';
import {
  getWaitlistWithBetaStatus,
  inviteUsersToBeta,
  getAllBetaInvitations,
  WaitlistEntry,
  BetaInvitation
} from '@/services/betaService';
import SecurityMonitor from '@/components/SecurityMonitor';
import DebugCacheManager from '@/components/DebugCacheManager';
import { debugLog, debugWarn, debugError } from '@/utils/debugLogger';

const WaitlistAdmin: React.FC = () => {
  const [entries, setEntries] = useState<WaitlistEntry[]>([]);
  const [invitations, setInvitations] = useState<BetaInvitation[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [inviting, setInviting] = useState(false);
  const { user } = useAuth();

  useEffect(() => {
    const fetchData = async () => {
      if (!user) {
        setError('You must be logged in to view this page');
        setLoading(false);
        return;
      }

      try {
        // Fetch waitlist with beta status
        const waitlistData = await getWaitlistWithBetaStatus();
        setEntries(waitlistData);

        // Fetch beta invitations
        const invitationsData = await getAllBetaInvitations();
        setInvitations(invitationsData);
      } catch (err) {
        debugError('Error fetching data:', err);
        setError('Failed to load data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [user]);

  const handleUserSelection = (userId: string, checked: boolean) => {
    if (checked) {
      setSelectedUsers(prev => [...prev, userId]);
    } else {
      setSelectedUsers(prev => prev.filter(id => id !== userId));
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      const pendingUsers = entries
        .filter(entry => entry.beta_status === 'pending')
        .map(entry => entry.id);
      setSelectedUsers(pendingUsers);
    } else {
      setSelectedUsers([]);
    }
  };

  const handleInviteToBeta = async () => {
    // if (selectedUsers.length === 0) {
    //   toast.error('No users selected', {
    //     description: 'Please select users to invite to beta.'
    //   });
    //   return;
    // }

    if (!user?.id) {
      toast.error('Authentication error', {
        description: 'You must be logged in to send invitations.'
      });
      return;
    }

    setInviting(true);

    let sendInvitationRandomly = false;

    if (selectedUsers.length == 0) {
      sendInvitationRandomly = true;
    }

    try {
      const { success, error } = await inviteUsersToBeta(selectedUsers, user.id, sendInvitationRandomly);

      if (!success) {
        toast.error('Failed to send invitations', {
          description: error || 'An unexpected error occurred.'
        });
        return;
      }

      toast.success(`Successfully invited ${selectedUsers.length} users to beta!`, {
        description: 'Invitation emails will be sent shortly.'
      });

      // Refresh data
      const waitlistData = await getWaitlistWithBetaStatus();
      setEntries(waitlistData);

      const invitationsData = await getAllBetaInvitations();
      setInvitations(invitationsData);

      // Clear selection
      setSelectedUsers([]);

    } catch (error) {
      debugError('Error inviting users:', error);
      toast.error('Failed to send invitations', {
        description: 'An unexpected error occurred.'
      });
    } finally {
      setInviting(false);
    }
  };

  const getBetaStatusBadge = (status: string) => {
    const variants = {
      pending: 'secondary',
      invited: 'default',
      accepted: 'success',
      declined: 'destructive'
    } as const;

    const colors = {
      pending: 'bg-gray-100 text-gray-800',
      invited: 'bg-blue-100 text-blue-800',
      accepted: 'bg-green-100 text-green-800',
      declined: 'bg-red-100 text-red-800'
    };

    return (
      <Badge className={colors[status as keyof typeof colors] || colors.pending}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const exportToCSV = () => {
    if (entries.length === 0) {
      toast.error('No data to export', {
        description: 'The waitlist is currently empty.'
      });
      return;
    }

    // Create CSV content
    const headers = ['Email', 'Referral Code', 'Beta Status', 'Joined Date', 'Beta Invited Date'];
    const csvRows = [
      headers.join(','),
      ...entries.map(entry => {
        const joinedDate = new Date(entry.created_at).toLocaleDateString();
        const betaInvitedDate = entry.beta_invited_at
          ? new Date(entry.beta_invited_at).toLocaleDateString()
          : '';
        return [
          `"${entry.email}"`,
          entry.referral_code ? `"${entry.referral_code}"` : '""',
          `"${entry.beta_status}"`,
          `"${joinedDate}"`,
          `"${betaInvitedDate}"`
        ].join(',');
      })
    ];
    const csvContent = csvRows.join('\n');

    // Create and download the file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `layrs-waitlist-${new Date().toISOString().split('T')[0]}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  if (!user) {
    return (
      <div className="container mx-auto px-4 py-12">
        <h1 className="text-2xl font-bold mb-4">Unauthorized</h1>
        <p>You must be logged in to view this page.</p>
      </div>
    );
  }

  const pendingUsers = entries.filter(entry => entry.beta_status === 'pending');
  const invitedUsers = entries.filter(entry => entry.beta_status === 'invited');
  const acceptedUsers = entries.filter(entry => entry.beta_status === 'accepted');

  return (
    <div className="container mx-auto px-4 py-12">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Waitlist Administration</h1>
        <div className="flex gap-2">
          <Button onClick={exportToCSV} disabled={loading || entries.length === 0} variant="outline">
            Export to CSV
          </Button>
          <Button
              onClick={handleInviteToBeta}
              className="bg-blue-600 hover:bg-blue-700 ml-2"
              disabled={inviting}
            >
              {inviting ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Inviting...
                </>
              ) : (
                <>
                  <Mail className="w-4 h-4 mr-2" />
                  Invite random users to Beta
                </>
              )}
            </Button>
          {selectedUsers.length > 0 && (
            <Button
              onClick={handleInviteToBeta}
              disabled={inviting}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {inviting ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Inviting...
                </>
              ) : (
                <>
                  <Mail className="w-4 h-4 mr-2" />
                  Invite {selectedUsers.length} to Beta
                </>
              )}
            </Button>
          )}
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-white p-4 rounded-lg shadow">
          <div className="flex items-center">
            <Users className="h-8 w-8 text-gray-500" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Users</p>
              <p className="text-2xl font-bold text-gray-900">{entries.length}</p>
            </div>
          </div>
        </div>
        <div className="bg-white p-4 rounded-lg shadow">
          <div className="flex items-center">
            <Users className="h-8 w-8 text-yellow-500" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Pending</p>
              <p className="text-2xl font-bold text-gray-900">{pendingUsers.length}</p>
            </div>
          </div>
        </div>
        <div className="bg-white p-4 rounded-lg shadow">
          <div className="flex items-center">
            <Mail className="h-8 w-8 text-blue-500" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Invited</p>
              <p className="text-2xl font-bold text-gray-900">{invitedUsers.length}</p>
            </div>
          </div>
        </div>
        <div className="bg-white p-4 rounded-lg shadow">
          <div className="flex items-center">
            <UserCheck className="h-8 w-8 text-green-500" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Beta Users</p>
              <p className="text-2xl font-bold text-gray-900">{acceptedUsers.length}</p>
            </div>
          </div>
        </div>
      </div>

      <Tabs defaultValue="waitlist" className="w-full">
        <TabsList>
          <TabsTrigger value="waitlist">Waitlist Management</TabsTrigger>
          <TabsTrigger value="invitations">Beta Invitations</TabsTrigger>
          <TabsTrigger value="security">Security Monitor</TabsTrigger>
          <TabsTrigger value="debug">Debug Cache</TabsTrigger>
        </TabsList>

        <TabsContent value="waitlist" className="mt-6">
          {loading ? (
            <div className="flex justify-center items-center py-12">
              <Loader2 className="w-8 h-8 animate-spin text-blue-500" />
              <span className="ml-2">Loading waitlist data...</span>
            </div>
          ) : error ? (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
              {error}
            </div>
          ) : entries.length === 0 ? (
            <div className="text-center py-12 text-gray-500">
              No entries in the waitlist yet.
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full bg-white rounded-lg overflow-hidden shadow">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left">
                      <Checkbox
                        checked={selectedUsers.length === pendingUsers.length && pendingUsers.length > 0}
                        onCheckedChange={handleSelectAll}
                        disabled={pendingUsers.length === 0}
                      />
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Email
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Beta Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Referral Code
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Joined Date
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Beta Invited
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {entries.map((entry) => (
                    <tr key={entry.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <Checkbox
                          checked={selectedUsers.includes(entry.id)}
                          onCheckedChange={(checked) => handleUserSelection(entry.id, checked as boolean)}
                          disabled={entry.beta_status !== 'pending'}
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {entry.email}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getBetaStatusBadge(entry.beta_status)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {entry.referral_code || '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(entry.created_at).toLocaleDateString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {entry.beta_invited_at ? new Date(entry.beta_invited_at).toLocaleDateString() : '-'}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
              <p className="mt-4 text-sm text-gray-500">
                Total entries: {entries.length} | Selected: {selectedUsers.length}
              </p>
            </div>
          )}
        </TabsContent>

        <TabsContent value="invitations" className="mt-6">
          <div className="overflow-x-auto">
            <table className="min-w-full bg-white rounded-lg overflow-hidden shadow">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Email
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Invited Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Expires
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Accepted Date
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {invitations.map((invitation) => (
                  <tr key={invitation.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {invitation.email}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getBetaStatusBadge(invitation.status)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(invitation.invited_at).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(invitation.expires_at).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {invitation.accepted_at ? new Date(invitation.accepted_at).toLocaleDateString() : '-'}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </TabsContent>

        <TabsContent value="security" className="mt-6">
          <SecurityMonitor />
        </TabsContent>

        <TabsContent value="debug" className="mt-6">
          <DebugCacheManager />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default WaitlistAdmin;
