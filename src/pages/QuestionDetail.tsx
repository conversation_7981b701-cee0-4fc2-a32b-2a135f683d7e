import React, { useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { useQuestions } from '@/contexts/QuestionsContext';
import Header from '@/components/Header';
import QuestionHeader from '@/components/question-detail/QuestionHeader';
import QuestionCard from '@/components/question-detail/QuestionCard';
import QuestionTabs from '@/components/question-detail/QuestionTabs';
import QuestionDesignSection from '@/components/question-detail/QuestionDesignSection';
import LoadingState from '@/components/question-detail/LoadingState';
import { debugLog, debugWarn, debugError } from '@/utils/debugLogger';

const QuestionDetail: React.FC = () => {
  const { questionId } = useParams<{ questionId: string }>();
  const navigate = useNavigate();
  const { getQuestionById, currentQuestion, setCurrentQuestion } = useQuestions();



  useEffect(() => {
    const loadQuestion = async () => {
      if (questionId) {
        try {
          const question = await getQuestionById(questionId);
          if (question) {
            setCurrentQuestion(question);
          } else {
            // Question not found, redirect to questions list
            navigate('/questions');
          }
        } catch (error) {
          debugError('Error loading question:', error);
          navigate('/questions');
        }
      }
    };

    loadQuestion();
  }, [questionId]); // Only depend on questionId, not the functions

  const handleStartAssessment = () => {
    // Navigate to the design page with the current question ID
    if (questionId) {
      navigate(`/design/${questionId}`);
    }
  };

  if (!currentQuestion) {
    return <LoadingState onStartAssessment={handleStartAssessment} />;
  }

  return (
    <div className="flex flex-col min-h-screen bg-gray-100">
      <Header onStartAssessment={handleStartAssessment} />

      <div className="flex-1 p-6">
        <div className="max-w-5xl mx-auto">
          <QuestionHeader question={currentQuestion} />
          <QuestionCard question={currentQuestion} onStartDesign={handleStartAssessment} />
          <QuestionTabs question={currentQuestion} />

          {questionId && <QuestionDesignSection questionId={questionId} contextType="question" contextId={questionId} />}
        </div>
      </div>
    </div>
  );
};

export default QuestionDetail;
