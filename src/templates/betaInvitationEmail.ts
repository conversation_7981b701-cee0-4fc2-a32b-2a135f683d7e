export const generateBetaInvitationEmail = (email: string, token: string) => {
  const invitationUrl = `${window.location.origin}/beta-invitation?token=${token}`;
  
  return {
    subject: "🎉 You're invited to Layrs Beta!",
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Layrs Beta Invitation</title>
        <style>
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8fafc;
          }
          .container {
            background: white;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          }
          .header {
            text-align: center;
            margin-bottom: 30px;
          }
          .logo {
            font-size: 24px;
            font-weight: bold;
            color: #1e1b4b;
            margin-bottom: 10px;
          }
          .title {
            font-size: 28px;
            font-weight: bold;
            color: #1e1b4b;
            margin-bottom: 10px;
          }
          .subtitle {
            font-size: 16px;
            color: #64748b;
            margin-bottom: 30px;
          }
          .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            color: white;
            text-decoration: none;
            padding: 16px 32px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            margin: 20px 0;
            text-align: center;
          }
          .features {
            background: #f1f5f9;
            border-radius: 8px;
            padding: 20px;
            margin: 30px 0;
          }
          .features h3 {
            color: #1e1b4b;
            margin-bottom: 15px;
          }
          .features ul {
            margin: 0;
            padding-left: 20px;
          }
          .features li {
            margin-bottom: 8px;
            color: #475569;
          }
          .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e2e8f0;
            color: #64748b;
            font-size: 14px;
          }
          .warning {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
            color: #92400e;
            font-size: 14px;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <div class="logo">Layrs</div>
            <h1 class="title">🎉 You're Invited to Beta!</h1>
            <p class="subtitle">
              Congratulations! You've been selected to join our exclusive beta program.
            </p>
          </div>

          <div style="text-align: center;">
            <a href="${invitationUrl}" class="cta-button">
              Accept Beta Invitation
            </a>
          </div>

          <div class="features">
            <h3>What you'll get with beta access:</h3>
            <ul>
              <li>🚀 Early access to new features before anyone else</li>
              <li>💬 Direct feedback channel to our development team</li>
              <li>🎯 Priority support and bug fixes</li>
              <li>👥 Access to exclusive beta user community</li>
              <li>📈 Influence the future direction of Layrs</li>
            </ul>
          </div>

          <div class="warning">
            <strong>⏰ Limited Time:</strong> This invitation expires in 7 days. 
            Don't miss out on being part of the future of system design!
          </div>

          <p>
            We're excited to have you as one of our beta users. Your feedback will be 
            invaluable in helping us build the best system design platform possible.
          </p>

          <p>
            If you have any questions or need help, feel free to reply to this email 
            or contact our support team.
          </p>

          <div class="footer">
            <p>
              This invitation was sent to <strong>${email}</strong><br>
              If you didn't request this invitation, you can safely ignore this email.
            </p>
            <p>
              © 2024 Layrs. All rights reserved.
            </p>
          </div>
        </div>
      </body>
      </html>
    `,
    text: `
🎉 You're invited to Layrs Beta!

Congratulations! You've been selected to join our exclusive beta program.

Accept your invitation: ${invitationUrl}

What you'll get with beta access:
• Early access to new features before anyone else
• Direct feedback channel to our development team  
• Priority support and bug fixes
• Access to exclusive beta user community
• Influence the future direction of Layrs

⏰ Limited Time: This invitation expires in 7 days.

We're excited to have you as one of our beta users. Your feedback will be invaluable in helping us build the best system design platform possible.

If you have any questions or need help, feel free to reply to this email or contact our support team.

This invitation was sent to ${email}
If you didn't request this invitation, you can safely ignore this email.

© 2024 Layrs. All rights reserved.
    `
  };
};
