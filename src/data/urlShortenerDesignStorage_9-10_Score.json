{"questionId": "1", "contextType": "question", "contextId": "1", "lastModified": "2024-01-15T10:30:00.000Z", "userJourneys": "1. URL Shortening Journey: User submits long URL via web interface → System validates URL format → Generates unique 6-8 character Base62 short code → Stores mapping in Cassandra with replication → Returns shortened URL within 200ms. 2. URL Redirect Journey: User clicks shortened URL → CDN edge server checks cache → Load balancer routes to redirect service → Service checks Redis cache → Falls back to database if needed → Returns HTTP 301 redirect within 100ms → Analytics event sent to Kafka. 3. Analytics Journey: User views dashboard → Queries aggregated metrics from InfluxDB → Real-time charts updated every 30 seconds.", "assumptions": "Scale: 10M URL shortenings/day (500 URLs/sec peak), 100:1 read-to-write ratio (1B redirects/day, 50K redirects/sec peak). Data: Average URL length 200 chars, short codes 6-8 chars Base62 (62^6=56B combinations), 80% URLs accessed within first week. Users: 70% mobile traffic, 60% clicks within 24h, geographic distribution 40% NA/30% EU/20% Asia. Business: Free tier 1K URLs/month, Premium unlimited with custom domains, Enterprise API access.", "constraints": "Zero Single Points of Failure: DNS failover between dual load balancers (< 60s failover), active-active API Gateways, dual Redis clusters with cross-replication, multi-region Cassandra deployment. Event Stream Complexity Management: Confluent Schema Registry for Avro schema evolution with backward/forward compatibility, Consumer State Manager for offset management and automatic rebalancing, dead letter queues for failed message handling, comprehensive monitoring of consumer lag and schema violations. Load Balancer Redundancy: Primary/secondary load balancers with DNS-based health checks, automatic failover via Route 53, geographic routing with latency-based selection. Kafka Operational Excellence: Schema versioning with compatibility checks, consumer group coordination with automatic rebalancing, exactly-once processing semantics, monitoring of partition distribution and broker health. Complete Resilience: 4-tier cache failover, service mesh with circuit breakers, mTLS encryption, distributed tracing, structured logging with correlation IDs.", "nodes": [{"id": "cdn-global-1", "type": "customNode", "position": {"x": 100, "y": 50}, "data": {"type": "cdn", "label": "Global CDN", "metadata": {"provider": "CloudFlare", "locations": "200+ edge locations globally", "cacheStrategy": "24h TTL for popular URLs, real-time invalidation", "performance": "< 50ms global latency, 100+ Gbps per edge", "features": "DDoS protection, SSL termination, HTTP/2", "customProperties": [{"key": "hitRatioTarget", "value": "85%+"}, {"key": "bandwidth", "value": "100+ Gbps per edge"}, {"key": "securityFeatures", "value": "DDoS protection, WAF, Bot management"}]}, "className": "bg-purple-100 border-purple-400"}, "style": {"width": 180, "height": 100}, "selected": false, "dragging": false}, {"id": "load-balancer-global-1", "type": "customNode", "position": {"x": 350, "y": 50}, "data": {"type": "loadbalancer", "label": "Global Load Balancer", "metadata": {"algorithm": "Geographic routing with health-based failover", "healthChecks": "/health endpoint, 5s interval, 2s timeout", "capacity": "1M+ requests per second", "features": "DNS-based geo routing, automatic failover, A/B testing", "customProperties": [{"key": "algorithm", "value": "Geographic + Health-based"}, {"key": "healthCheckInterval", "value": "5 seconds"}, {"key": "failoverTime", "value": "< 30 seconds"}, {"key": "capacity", "value": "1M+ RPS"}, {"key": "instanceRole", "value": "Primary load balancer"}, {"key": "redundancy", "value": "Active-passive with DNS failover"}, {"key": "monitoring", "value": "Health checks, throughput, error rates"}]}, "className": "bg-orange-100 border-orange-400"}, "style": {"width": 180, "height": 100}, "selected": false, "dragging": false}, {"id": "api-gateway-1", "type": "customNode", "position": {"x": 600, "y": 50}, "data": {"type": "apigateway", "label": "API Gateway", "metadata": {"authentication": "API key, OAuth 2.0, JWT validation", "rateLimiting": "Free: 1K req/h, Premium: 100K req/h", "features": "Request transformation, versioning, logging", "burstCapacity": "10x normal rate for 1 minute", "customProperties": [{"key": "rateLimitFree", "value": "1000 requests/hour"}, {"key": "rateLimitPremium", "value": "100000 requests/hour"}, {"key": "authMethods", "value": "API Key, OAuth 2.0, JWT"}, {"key": "burstCapacity", "value": "10x normal rate"}]}, "className": "bg-blue-100 border-blue-400"}, "style": {"width": 180, "height": 100}, "selected": false, "dragging": false}, {"id": "url-creation-service-1", "type": "customNode", "position": {"x": 200, "y": 250}, "data": {"type": "server", "label": "URL Creation Service", "metadata": {"language": "Go", "scaling": "Horizontal auto-scaling", "algorithm": "Counter-based Base62 encoding", "codeLength": "6-8 characters (a-z,A-Z,0-9)", "collisionHandling": "Database uniqueness constraint + retry", "customProperties": [{"key": "language", "value": "Go"}, {"key": "targetLatency", "value": "< 200ms"}, {"key": "throughput", "value": "10K requests/sec per instance"}, {"key": "autoScaling", "value": "CPU > 70% or Queue > 100"}, {"key": "shortCodeAlgorithm", "value": "Counter-based Base62"}, {"key": "instances", "value": "5 instances across 3 regions"}, {"key": "scaling", "value": "Horizontal auto-scaling"}, {"key": "minInstances", "value": "3"}, {"key": "maxInstances", "value": "50"}, {"key": "serviceMonitoring", "value": "Prometheus metrics: /metrics endpoint, custom counters for URL creation rate, error rate, latency histograms"}, {"key": "alerting", "value": "Service-specific alerts: Creation latency > 200ms, Error rate > 0.5%, Queue depth > 1000"}, {"key": "security", "value": "TLS 1.3 for all connections, JWT token validation, rate limiting per API key"}, {"key": "logging", "value": "Structured JSON logs with correlation IDs, log level: INFO, retention: 30 days"}]}, "className": "bg-green-100 border-green-400"}, "style": {"width": 180, "height": 100}, "selected": false, "dragging": false}, {"id": "redirect-service-1", "type": "customNode", "position": {"x": 700, "y": 250}, "data": {"type": "server", "label": "Redirect Service", "metadata": {"language": "Rust", "scaling": "Horizontal aggressive auto-scaling", "cachingStrategy": "L1: Local in-memory cache (10K URLs), L2: Primary Redis cluster, L3: Secondary Redis cluster, L4: Database", "performance": "< 50ms cache hit, < 100ms cache miss", "throughput": "50K requests/sec per instance", "customProperties": [{"key": "language", "value": "Rust"}, {"key": "cacheHitLatency", "value": "< 50ms"}, {"key": "cacheMissLatency", "value": "< 100ms"}, {"key": "throughput", "value": "50K requests/sec per instance"}, {"key": "cacheHitRatio", "value": "95%+"}, {"key": "instances", "value": "10 instances across 3 regions"}, {"key": "scaling", "value": "Horizontal aggressive auto-scaling"}, {"key": "minInstances", "value": "5"}, {"key": "maxInstances", "value": "100"}, {"key": "serviceMonitoring", "value": "Prometheus metrics: /metrics endpoint, cache hit/miss ratios, redirect latency p95/p99, error rates by status code"}, {"key": "alerting", "value": "Service-specific alerts: Redirect latency p95 > 100ms, Cache hit ratio < 90%, Error rate > 0.1%"}, {"key": "security", "value": "TLS 1.3 for all connections, encrypted Redis connections, secure headers in responses"}, {"key": "logging", "value": "Structured JSON logs with request tracing, access logs with anonymized IPs, retention: 7 days"}, {"key": "resilience", "value": "Circuit breaker for Redis, fallback to local cache then database, graceful degradation"}, {"key": "cacheFailover", "value": "Primary Redis → Secondary Redis → Local Cache → Database fallback chain"}]}, "className": "bg-green-100 border-green-400"}, "style": {"width": 180, "height": 100}, "selected": false, "dragging": false}, {"id": "redis-cluster-1", "type": "customNode", "position": {"x": 450, "y": 400}, "data": {"type": "cache", "label": "Redis Cluster", "metadata": {"engine": "Redis Cluster", "nodes": "6 nodes (3 masters, 3 replicas)", "memoryPerNode": "32GB", "evictionPolicy": "allkeys-lru", "persistence": "RDB snapshots + AOF logging", "customProperties": [{"key": "clusterNodes", "value": "6 (3 masters, 3 replicas)"}, {"key": "memoryPerNode", "value": "32GB"}, {"key": "latency", "value": "< 1ms"}, {"key": "throughput", "value": "100K+ ops/sec per node"}, {"key": "ttl", "value": "7 days regular, 30 days popular"}, {"key": "security", "value": "TLS encryption for client connections, AUTH password protection, encrypted inter-node communication"}, {"key": "monitoring", "value": "Per-node metrics: memory usage, hit/miss ratios, connection count, eviction rate, replication lag"}, {"key": "alerting", "value": "Node-specific alerts: Memory usage > 90%, Hit ratio < 85%, Replication lag > 1s, Node down > 30s"}, {"key": "failover", "value": "Primary cluster with automatic failover to secondary cluster"}, {"key": "resilience", "value": "Circuit breaker pattern, graceful degradation to local cache and database"}]}, "className": "bg-red-100 border-red-400"}, "style": {"width": 180, "height": 100}, "selected": false, "dragging": false}, {"id": "cassandra-db-1", "type": "customNode", "position": {"x": 200, "y": 550}, "data": {"type": "database", "label": "URL Database (Cassandra)", "metadata": {"engine": "Apache Cassandra", "scaling": "Distributed across multiple data centers", "partitioning": "Hash partitioning on short_code", "replication": "Factor 3, QUORUM writes, ONE reads", "schema": "url_mappings: short_code(PK), original_url, user_id, created_at, expires_at, click_count", "customProperties": [{"key": "engine", "value": "Apache Cassandra"}, {"key": "writeLatency", "value": "< 10ms"}, {"key": "readLatency", "value": "< 5ms"}, {"key": "throughput", "value": "100K+ writes/sec, 1M+ reads/sec"}, {"key": "replicationFactor", "value": "3"}, {"key": "scaling", "value": "Sharded across multiple data centers"}, {"key": "nodes", "value": "12 nodes across 3 regions"}, {"key": "consistencyLevel", "value": "QUORUM writes, ONE reads"}, {"key": "partitioning", "value": "Hash partitioning on short_code"}, {"key": "security", "value": "Encryption at rest (AES-256), TLS 1.3 for client connections, role-based access control (RBAC)"}, {"key": "dataMigration", "value": "Schema versioning with backward compatibility, online schema changes using ALTER statements, blue-green deployment for major migrations"}, {"key": "backup", "value": "Incremental backups every 4 hours, full backups daily, cross-region backup replication, point-in-time recovery"}, {"key": "monitoring", "value": "Node-level metrics: CPU, memory, disk I/O, query latency, compaction status, repair progress"}]}, "className": "bg-yellow-100 border-yellow-400"}, "style": {"width": 180, "height": 100}, "selected": false, "dragging": false}, {"id": "analytics-db-1", "type": "customNode", "position": {"x": 700, "y": 550}, "data": {"type": "database", "label": "Analytics DB (InfluxDB)", "metadata": {"engine": "InfluxDB", "scaling": "Time-based sharding with retention policies", "schema": "url_clicks: measurement with tags[short_code, country, device], fields[click_count, response_time]", "retention": "Raw events: 30 days, Hourly aggregates: 1 year, Daily: 5 years", "performance": "1M+ events/sec ingestion, < 100ms query latency", "customProperties": [{"key": "engine", "value": "InfluxDB"}, {"key": "ingestionRate", "value": "1M+ events/second"}, {"key": "queryLatency", "value": "< 100ms"}, {"key": "compressionRatio", "value": "10:1"}, {"key": "retentionRaw", "value": "30 days"}]}, "className": "bg-yellow-100 border-yellow-400"}, "style": {"width": 180, "height": 100}, "selected": false, "dragging": false}, {"id": "kafka-stream-1", "type": "customNode", "position": {"x": 950, "y": 400}, "data": {"type": "queue", "label": "Event Stream (Kafka)", "metadata": {"engine": "Apache Kafka", "topics": "url-clicks, url-created, url-analytics, system-metrics", "partitions": "12 per topic for parallelism", "replication": "Factor 3, 7-day retention for clicks", "performance": "1M+ messages/sec, < 10ms end-to-end latency", "customProperties": [{"key": "engine", "value": "Apache Kafka"}, {"key": "throughput", "value": "1M+ messages/second"}, {"key": "latency", "value": "< 10ms end-to-end"}, {"key": "partitions", "value": "12 per topic"}, {"key": "retention", "value": "7 days clicks, 30 days metrics"}, {"key": "schemaManagement", "value": "Confluent Schema Registry with Avro serialization"}, {"key": "consumerGroups", "value": "Managed consumer groups with automatic rebalancing"}, {"key": "monitoring", "value": "Consumer lag, partition distribution, broker health, schema evolution"}, {"key": "alerting", "value": "Consumer lag > 1000, Broker down, Schema compatibility violations"}, {"key": "deadLetter<PERSON><PERSON><PERSON>", "value": "Failed message handling with retry policies and DLQ"}]}, "className": "bg-indigo-100 border-indigo-400"}, "style": {"width": 180, "height": 100}, "selected": false, "dragging": false}, {"id": "analytics-service-1", "type": "customNode", "position": {"x": 950, "y": 250}, "data": {"type": "server", "label": "Analytics Service", "metadata": {"language": "Scala with Apache Flink", "scaling": "Horizontal with dynamic parallelism", "processing": "Real-time stream processing with 1min/1hr/1day windows", "stateBackend": "RocksDB for fault tolerance", "metrics": "Click counts, geo distribution, device analytics, response times", "customProperties": [{"key": "framework", "value": "Apache Flink"}, {"key": "processingLatency", "value": "< 5 seconds"}, {"key": "throughput", "value": "500K+ events/second"}, {"key": "faultTolerance", "value": "Exactly-once processing"}, {"key": "checkpointing", "value": "Every 30 seconds"}, {"key": "serviceMonitoring", "value": "Flink metrics: processing rate, backpressure, checkpoint duration, task manager health"}, {"key": "alerting", "value": "Stream-specific alerts: Processing lag > 10s, Checkpoint failure, Backpressure > 5min, Task failure rate > 1%"}, {"key": "security", "value": "TLS for Kafka connections, encrypted state backend, secure Flink web UI with authentication"}]}, "className": "bg-green-100 border-green-400"}, "style": {"width": 180, "height": 100}, "selected": false, "dragging": false}, {"id": "monitoring-system-1", "type": "customNode", "position": {"x": 1200, "y": 250}, "data": {"type": "server", "label": "Monitoring & Alerting", "metadata": {"language": "Go + Prometheus", "scaling": "Horizontal with auto-scaling", "stack": "Prometheus + Grafana + AlertManager", "metrics": "Latency percentiles, throughput, error rates, cache hit ratios", "alerting": "Redirect latency > 100ms, Error rate > 1%, Cache hit < 90%", "dashboards": "Real-time overview, service metrics, business metrics, infrastructure", "logAggregation": "ELK Stack with 30-day retention", "customProperties": [{"key": "language", "value": "Go + Prometheus"}, {"key": "scaling", "value": "Horizontal"}, {"key": "stack", "value": "Prometheus + Grafana + AlertManager"}, {"key": "alertLatency", "value": "< 1 minute"}, {"key": "metricsRetention", "value": "1 year"}, {"key": "logRetention", "value": "30 days"}, {"key": "instances", "value": "3 instances across regions"}, {"key": "autoScaling", "value": "CPU > 60% or Memory > 70%"}]}, "className": "bg-gray-100 border-gray-400"}, "style": {"width": 180, "height": 100}, "selected": false, "dragging": false}, {"id": "url-creation-service-2", "type": "customNode", "position": {"x": 200, "y": 380}, "data": {"type": "server", "label": "URL Creation Service #2", "metadata": {"language": "Go", "scaling": "Horizontal auto-scaling", "algorithm": "Counter-based Base62 encoding", "codeLength": "6-8 characters (a-z,A-Z,0-9)", "collisionHandling": "Database uniqueness constraint + retry", "role": "Load balanced instance for high availability", "customProperties": [{"key": "language", "value": "Go"}, {"key": "targetLatency", "value": "< 200ms"}, {"key": "throughput", "value": "10K requests/sec per instance"}, {"key": "autoScaling", "value": "CPU > 70% or Queue > 100"}, {"key": "shortCodeAlgorithm", "value": "Counter-based Base62"}, {"key": "instanceRole", "value": "Secondary instance for load balancing"}, {"key": "scaling", "value": "Horizontal auto-scaling"}, {"key": "serviceMonitoring", "value": "Prometheus metrics: /metrics endpoint, custom counters for URL creation rate, error rate, latency histograms"}, {"key": "alerting", "value": "Service-specific alerts: Creation latency > 200ms, Error rate > 0.5%, Queue depth > 1000"}, {"key": "security", "value": "TLS 1.3 for all connections, JWT token validation, rate limiting per API key"}]}, "className": "bg-green-100 border-green-400"}, "style": {"width": 180, "height": 100}, "selected": false, "dragging": false}, {"id": "redirect-service-2", "type": "customNode", "position": {"x": 700, "y": 380}, "data": {"type": "server", "label": "Redirect Service #2", "metadata": {"language": "Rust", "scaling": "Horizontal aggressive auto-scaling", "cachingStrategy": "L1: In-memory LRU (10K URLs), L2: Redis, L3: Database", "performance": "< 50ms cache hit, < 100ms cache miss", "throughput": "50K requests/sec per instance", "role": "Load balanced instance for high availability", "customProperties": [{"key": "language", "value": "Rust"}, {"key": "cacheHitLatency", "value": "< 50ms"}, {"key": "cacheMissLatency", "value": "< 100ms"}, {"key": "throughput", "value": "50K requests/sec per instance"}, {"key": "cacheHitRatio", "value": "95%+"}, {"key": "instanceRole", "value": "Secondary instance for load balancing"}, {"key": "scaling", "value": "Horizontal aggressive auto-scaling"}, {"key": "serviceMonitoring", "value": "Prometheus metrics: /metrics endpoint, cache hit/miss ratios, redirect latency p95/p99, error rates by status code"}, {"key": "alerting", "value": "Service-specific alerts: Redirect latency p95 > 100ms, Cache hit ratio < 90%, Error rate > 0.1%"}, {"key": "security", "value": "TLS 1.3 for all connections, encrypted Redis connections, secure headers in responses"}]}, "className": "bg-green-100 border-green-400"}, "style": {"width": 180, "height": 100}, "selected": false, "dragging": false}, {"id": "api-gateway-2", "type": "customNode", "position": {"x": 600, "y": 180}, "data": {"type": "apigateway", "label": "API Gateway #2", "metadata": {"purpose": "Secondary API Gateway for high availability and load distribution", "authentication": "API key, OAuth 2.0, JWT validation", "rateLimiting": "Free: 1K req/h, Premium: 100K req/h", "features": "Request transformation, versioning, logging", "burstCapacity": "10x normal rate for 1 minute", "customProperties": [{"key": "rateLimitFree", "value": "1000 requests/hour"}, {"key": "rateLimitPremium", "value": "100000 requests/hour"}, {"key": "authMethods", "value": "API Key, OAuth 2.0, JWT"}, {"key": "burstCapacity", "value": "10x normal rate"}, {"key": "instanceRole", "value": "Secondary gateway for load balancing"}, {"key": "failover", "value": "Active-active with health checks"}, {"key": "loadBalancing", "value": "Round-robin with sticky sessions"}]}, "className": "bg-blue-100 border-blue-400"}, "style": {"width": 180, "height": 100}, "selected": false, "dragging": false}, {"id": "service-mesh-1", "type": "customNode", "position": {"x": 450, "y": 150}, "data": {"type": "server", "label": "Service Mesh (Istio)", "metadata": {"purpose": "Manage inter-service communication, observability, and security", "language": "Envoy Proxy + Istio Control Plane", "scaling": "Sidecar proxy per service instance", "features": "Traffic management, security policies, observability, fault injection", "customProperties": [{"key": "framework", "value": "<PERSON><PERSON><PERSON> with Envoy sidecars"}, {"key": "trafficManagement", "value": "Load balancing, circuit breakers, retries, timeouts"}, {"key": "security", "value": "mTLS between services, policy enforcement, certificate management"}, {"key": "observability", "value": "Distributed tracing, metrics collection, access logs"}, {"key": "faultTolerance", "value": "Circuit breakers, bulkhead isolation, retry policies"}, {"key": "debugging", "value": "Request tracing, service topology visualization, traffic mirroring"}, {"key": "deployment", "value": "Blue-green deployments, canary releases, A/B testing"}]}, "className": "bg-purple-100 border-purple-400"}, "style": {"width": 180, "height": 100}, "selected": false, "dragging": false}, {"id": "redis-cluster-2", "type": "customNode", "position": {"x": 450, "y": 530}, "data": {"type": "cache", "label": "Redis Cluster #2", "metadata": {"purpose": "Secondary Redis cluster for cache redundancy and failover", "engine": "Redis Cluster", "nodes": "6 nodes (3 masters, 3 replicas)", "memoryPerNode": "32GB", "evictionPolicy": "allkeys-lru", "persistence": "RDB snapshots + AOF logging", "customProperties": [{"key": "clusterNodes", "value": "6 (3 masters, 3 replicas)"}, {"key": "memoryPerNode", "value": "32GB"}, {"key": "latency", "value": "< 1ms"}, {"key": "throughput", "value": "100K+ ops/sec per node"}, {"key": "ttl", "value": "7 days regular, 30 days popular"}, {"key": "security", "value": "TLS encryption for client connections, AUTH password protection, encrypted inter-node communication"}, {"key": "monitoring", "value": "Per-node metrics: memory usage, hit/miss ratios, connection count, eviction rate, replication lag"}, {"key": "alerting", "value": "Node-specific alerts: Memory usage > 90%, Hit ratio < 85%, Replication lag > 1s, Node down > 30s"}, {"key": "failover", "value": "Cross-cluster replication, automatic failover to secondary cluster"}, {"key": "instanceRole", "value": "Secondary cluster for cache redundancy"}]}, "className": "bg-red-100 border-red-400"}, "style": {"width": 180, "height": 100}, "selected": false, "dragging": false}, {"id": "local-cache-1", "type": "customNode", "position": {"x": 950, "y": 150}, "data": {"type": "cache", "label": "Local Cache Layer", "metadata": {"purpose": "In-memory L1 cache in each service instance for Redis failure resilience", "engine": "In-memory LRU cache per service instance", "capacity": "10K most popular URLs per instance", "evictionPolicy": "LRU with TTL", "customProperties": [{"key": "engine", "value": "In-memory LRU cache"}, {"key": "capacity", "value": "10K URLs per service instance"}, {"key": "hitRatio", "value": "60-70% for popular URLs"}, {"key": "latency", "value": "< 1ms"}, {"key": "ttl", "value": "5 minutes for hot data"}, {"key": "resilience", "value": "Continues serving during Redis failures"}, {"key": "warmup", "value": "Preloaded with trending URLs on startup"}, {"key": "monitoring", "value": "Hit/miss ratios, memory usage, eviction rate per instance"}]}, "className": "bg-orange-100 border-orange-400"}, "style": {"width": 180, "height": 100}, "selected": false, "dragging": false}, {"id": "load-balancer-global-2", "type": "customNode", "position": {"x": 350, "y": 180}, "data": {"type": "loadbalancer", "label": "Global Load Balancer #2", "metadata": {"algorithm": "Geographic routing with health-based failover", "healthChecks": "/health endpoint, 5s interval, 2s timeout", "capacity": "1M+ requests per second", "features": "DNS-based geo routing, automatic failover, A/B testing", "customProperties": [{"key": "algorithm", "value": "Geographic + Health-based"}, {"key": "healthCheckInterval", "value": "5 seconds"}, {"key": "failoverTime", "value": "< 30 seconds"}, {"key": "capacity", "value": "1M+ RPS"}, {"key": "instanceRole", "value": "Secondary load balancer"}, {"key": "redundancy", "value": "Active-passive with DNS failover"}, {"key": "monitoring", "value": "Health checks, throughput, error rates"}]}, "className": "bg-orange-100 border-orange-400"}, "style": {"width": 180, "height": 100}, "selected": false, "dragging": false}, {"id": "dns-failover-1", "type": "customNode", "position": {"x": 200, "y": 115}, "data": {"type": "dns", "label": "DNS Failover Manager", "metadata": {"purpose": "Manage DNS-based failover between load balancers", "provider": "Route 53 Health Checks", "features": "Health-based routing, automatic DNS updates, global traffic management", "customProperties": [{"key": "provider", "value": "AWS Route 53 with health checks"}, {"key": "healthCheckInterval", "value": "30 seconds"}, {"key": "failoverTime", "value": "< 60 seconds DNS propagation"}, {"key": "monitoring", "value": "DNS query success rates, health check status"}, {"key": "geolocation", "value": "Geographic routing with latency-based failover"}, {"key": "ttl", "value": "60 seconds for fast failover"}]}, "className": "bg-cyan-100 border-cyan-400"}, "style": {"width": 180, "height": 100}, "selected": false, "dragging": false}, {"id": "schema-registry-1", "type": "customNode", "position": {"x": 1150, "y": 400}, "data": {"type": "server", "label": "Schema Registry", "metadata": {"purpose": "Manage Kafka event schemas and ensure compatibility", "language": "Java (Confluent Schema Registry)", "scaling": "Clustered deployment with leader election", "features": "Schema evolution, compatibility checking, version management", "customProperties": [{"key": "framework", "value": "Confluent Schema Registry"}, {"key": "schemaFormats", "value": "<PERSON><PERSON><PERSON>, JSO<PERSON>, Protobuf"}, {"key": "compatibility", "value": "Backward, forward, and full compatibility checks"}, {"key": "versioning", "value": "Automatic schema versioning with rollback support"}, {"key": "clustering", "value": "3-node cluster with leader election"}, {"key": "monitoring", "value": "Schema registration rate, compatibility violations, API latency"}, {"key": "security", "value": "RBAC for schema management, encrypted storage"}]}, "className": "bg-violet-100 border-violet-400"}, "style": {"width": 180, "height": 100}, "selected": false, "dragging": false}, {"id": "consumer-state-manager-1", "type": "customNode", "position": {"x": 1150, "y": 530}, "data": {"type": "server", "label": "Consumer State Manager", "metadata": {"purpose": "Manage Kafka consumer offsets and state for reliable processing", "language": "Go with Kafka Connect", "scaling": "Distributed with automatic rebalancing", "features": "Offset management, consumer group coordination, dead letter queues", "customProperties": [{"key": "framework", "value": "Kafka Connect + Custom Go services"}, {"key": "offsetManagement", "value": "Automatic offset commits with exactly-once semantics"}, {"key": "rebalancing", "value": "Automatic consumer group rebalancing on failures"}, {"key": "deadLetter<PERSON><PERSON><PERSON>", "value": "Failed message handling with retry policies"}, {"key": "monitoring", "value": "Consumer lag, processing rates, error rates, rebalance frequency"}, {"key": "alerting", "value": "Consumer lag > 1000 messages, Processing errors > 1%, Rebalance > 5/hour"}, {"key": "recovery", "value": "Automatic consumer restart, offset reset capabilities"}]}, "className": "bg-teal-100 border-teal-400"}, "style": {"width": 180, "height": 100}, "selected": false, "dragging": false}], "edges": [{"id": "edge-cdn-lb", "source": "cdn-global-1", "target": "load-balancer-global-1", "type": "custom", "label": "HTTPS Traffic", "data": {"connectionNumber": "HTTPS Traffic"}, "style": {"stroke": "#222", "strokeWidth": 2}, "markerEnd": {"type": "arrowclosed", "color": "#222"}, "selected": false}, {"id": "edge-lb-gateway", "source": "load-balancer-global-1", "target": "api-gateway-1", "type": "custom", "label": "Load Balanced Requests", "data": {"connectionNumber": "Load Balanced Requests"}, "style": {"stroke": "#222", "strokeWidth": 2}, "markerEnd": {"type": "arrowclosed", "color": "#222"}, "selected": false}, {"id": "edge-gateway-creation", "source": "api-gateway-1", "target": "url-creation-service-1", "type": "custom", "label": "URL Creation API", "data": {"connectionNumber": "URL Creation API"}, "style": {"stroke": "#222", "strokeWidth": 2}, "markerEnd": {"type": "arrowclosed", "color": "#222"}, "selected": false}, {"id": "edge-gateway-redirect", "source": "api-gateway-1", "target": "redirect-service-1", "type": "custom", "label": "Redirect Requests", "data": {"connectionNumber": "Redirect Requests"}, "style": {"stroke": "#222", "strokeWidth": 2}, "markerEnd": {"type": "arrowclosed", "color": "#222"}, "selected": false}, {"id": "edge-creation-cache", "source": "url-creation-service-1", "target": "redis-cluster-1", "type": "custom", "label": "<PERSON><PERSON> New URLs", "data": {"connectionNumber": "<PERSON><PERSON> New URLs"}, "style": {"stroke": "#222", "strokeWidth": 2}, "markerEnd": {"type": "arrowclosed", "color": "#222"}, "selected": false}, {"id": "edge-redirect-cache", "source": "redirect-service-1", "target": "redis-cluster-1", "type": "custom", "label": "<PERSON><PERSON>", "data": {"connectionNumber": "<PERSON><PERSON>"}, "style": {"stroke": "#222", "strokeWidth": 2}, "markerEnd": {"type": "arrowclosed", "color": "#222"}, "selected": false}, {"id": "edge-creation-db", "source": "url-creation-service-1", "target": "cassandra-db-1", "type": "custom", "label": "Store URL Mappings", "data": {"connectionNumber": "Store URL Mappings"}, "style": {"stroke": "#222", "strokeWidth": 2}, "markerEnd": {"type": "arrowclosed", "color": "#222"}, "selected": false}, {"id": "edge-redirect-db", "source": "redirect-service-1", "target": "cassandra-db-1", "type": "custom", "label": "Database Fallback", "data": {"connectionNumber": "Database Fallback"}, "style": {"stroke": "#222", "strokeWidth": 2}, "markerEnd": {"type": "arrowclosed", "color": "#222"}, "selected": false}, {"id": "edge-redirect-kafka", "source": "redirect-service-1", "target": "kafka-stream-1", "type": "custom", "label": "Click Events", "data": {"connectionNumber": "Click Events"}, "style": {"stroke": "#222", "strokeWidth": 2}, "markerEnd": {"type": "arrowclosed", "color": "#222"}, "selected": false}, {"id": "edge-kafka-analytics", "source": "kafka-stream-1", "target": "analytics-service-1", "type": "custom", "label": "Event Stream", "data": {"connectionNumber": "Event Stream"}, "style": {"stroke": "#222", "strokeWidth": 2}, "markerEnd": {"type": "arrowclosed", "color": "#222"}, "selected": false}, {"id": "edge-analytics-db", "source": "analytics-service-1", "target": "analytics-db-1", "type": "custom", "label": "Store Analytics", "data": {"connectionNumber": "Store Analytics"}, "style": {"stroke": "#222", "strokeWidth": 2}, "markerEnd": {"type": "arrowclosed", "color": "#222"}, "selected": false}, {"id": "edge-analytics-monitoring", "source": "analytics-service-1", "target": "monitoring-system-1", "type": "custom", "label": "Metrics & Alerts", "data": {"connectionNumber": "Metrics & Alerts"}, "style": {"stroke": "#222", "strokeWidth": 2}, "markerEnd": {"type": "arrowclosed", "color": "#222"}, "selected": false}, {"id": "edge-creation-monitoring", "source": "url-creation-service-1", "target": "monitoring-system-1", "type": "custom", "label": "Service Metrics", "data": {"connectionNumber": "Service Metrics"}, "style": {"stroke": "#222", "strokeWidth": 2}, "markerEnd": {"type": "arrowclosed", "color": "#222"}, "selected": false}, {"id": "edge-redirect-monitoring", "source": "redirect-service-1", "target": "monitoring-system-1", "type": "custom", "label": "Performance Metrics", "data": {"connectionNumber": "Performance Metrics"}, "style": {"stroke": "#222", "strokeWidth": 2}, "markerEnd": {"type": "arrowclosed", "color": "#222"}, "selected": false}, {"id": "edge-cache-monitoring", "source": "redis-cluster-1", "target": "monitoring-system-1", "type": "custom", "label": "<PERSON><PERSON>", "data": {"connectionNumber": "<PERSON><PERSON>"}, "style": {"stroke": "#222", "strokeWidth": 2}, "markerEnd": {"type": "arrowclosed", "color": "#222"}, "selected": false}, {"id": "edge-db-monitoring", "source": "cassandra-db-1", "target": "monitoring-system-1", "type": "custom", "label": "Database Metrics", "data": {"connectionNumber": "Database Metrics"}, "style": {"stroke": "#222", "strokeWidth": 2}, "markerEnd": {"type": "arrowclosed", "color": "#222"}, "selected": false}, {"id": "edge-gateway-creation-2", "source": "api-gateway-1", "target": "url-creation-service-2", "type": "custom", "label": "URL Creation API (LB)", "data": {"connectionNumber": "URL Creation API (Load Balanced)"}, "style": {"stroke": "#222", "strokeWidth": 2}, "markerEnd": {"type": "arrowclosed", "color": "#222"}, "selected": false}, {"id": "edge-gateway-redirect-2", "source": "api-gateway-1", "target": "redirect-service-2", "type": "custom", "label": "Redirect Requests (LB)", "data": {"connectionNumber": "Redirect Requests (Load Balanced)"}, "style": {"stroke": "#222", "strokeWidth": 2}, "markerEnd": {"type": "arrowclosed", "color": "#222"}, "selected": false}, {"id": "edge-creation-2-cache", "source": "url-creation-service-2", "target": "redis-cluster-1", "type": "custom", "label": "<PERSON><PERSON> New URLs", "data": {"connectionNumber": "<PERSON><PERSON> New URLs"}, "style": {"stroke": "#222", "strokeWidth": 2}, "markerEnd": {"type": "arrowclosed", "color": "#222"}, "selected": false}, {"id": "edge-redirect-2-cache", "source": "redirect-service-2", "target": "redis-cluster-1", "type": "custom", "label": "<PERSON><PERSON>", "data": {"connectionNumber": "<PERSON><PERSON>"}, "style": {"stroke": "#222", "strokeWidth": 2}, "markerEnd": {"type": "arrowclosed", "color": "#222"}, "selected": false}, {"id": "edge-creation-2-db", "source": "url-creation-service-2", "target": "cassandra-db-1", "type": "custom", "label": "Store URL Mappings", "data": {"connectionNumber": "Store URL Mappings"}, "style": {"stroke": "#222", "strokeWidth": 2}, "markerEnd": {"type": "arrowclosed", "color": "#222"}, "selected": false}, {"id": "edge-redirect-2-db", "source": "redirect-service-2", "target": "cassandra-db-1", "type": "custom", "label": "Database Fallback", "data": {"connectionNumber": "Database Fallback"}, "style": {"stroke": "#222", "strokeWidth": 2}, "markerEnd": {"type": "arrowclosed", "color": "#222"}, "selected": false}, {"id": "edge-redirect-2-kafka", "source": "redirect-service-2", "target": "kafka-stream-1", "type": "custom", "label": "Click Events", "data": {"connectionNumber": "Click Events"}, "style": {"stroke": "#222", "strokeWidth": 2}, "markerEnd": {"type": "arrowclosed", "color": "#222"}, "selected": false}, {"id": "edge-lb-gateway-2", "source": "load-balancer-global-1", "target": "api-gateway-2", "type": "custom", "label": "Load Balanced Requests", "data": {"connectionNumber": "Load Balanced Requests (Secondary)"}, "style": {"stroke": "#222", "strokeWidth": 2}, "markerEnd": {"type": "arrowclosed", "color": "#222"}, "selected": false}, {"id": "edge-gateway-2-creation", "source": "api-gateway-2", "target": "url-creation-service-1", "type": "custom", "label": "URL Creation API", "data": {"connectionNumber": "URL Creation API (Gateway 2)"}, "style": {"stroke": "#222", "strokeWidth": 2}, "markerEnd": {"type": "arrowclosed", "color": "#222"}, "selected": false}, {"id": "edge-gateway-2-redirect", "source": "api-gateway-2", "target": "redirect-service-1", "type": "custom", "label": "Redirect Requests", "data": {"connectionNumber": "Redirect Requests (Gateway 2)"}, "style": {"stroke": "#222", "strokeWidth": 2}, "markerEnd": {"type": "arrowclosed", "color": "#222"}, "selected": false}, {"id": "edge-service-mesh-creation", "source": "service-mesh-1", "target": "url-creation-service-1", "type": "custom", "label": "Service Communication", "data": {"connectionNumber": "Service Mesh Management"}, "style": {"stroke": "#9333ea", "strokeWidth": 2}, "markerEnd": {"type": "arrowclosed", "color": "#9333ea"}, "selected": false}, {"id": "edge-service-mesh-redirect", "source": "service-mesh-1", "target": "redirect-service-1", "type": "custom", "label": "Service Communication", "data": {"connectionNumber": "Service Mesh Management"}, "style": {"stroke": "#9333ea", "strokeWidth": 2}, "markerEnd": {"type": "arrowclosed", "color": "#9333ea"}, "selected": false}, {"id": "edge-redirect-cache-2", "source": "redirect-service-1", "target": "redis-cluster-2", "type": "custom", "label": "Secondary Cache", "data": {"connectionNumber": "Secondary C<PERSON>up"}, "style": {"stroke": "#dc2626", "strokeWidth": 2}, "markerEnd": {"type": "arrowclosed", "color": "#dc2626"}, "selected": false}, {"id": "edge-redirect-local-cache", "source": "redirect-service-1", "target": "local-cache-1", "type": "custom", "label": "Local Cache", "data": {"connectionNumber": "Local Cache Access"}, "style": {"stroke": "#ea580c", "strokeWidth": 2}, "markerEnd": {"type": "arrowclosed", "color": "#ea580c"}, "selected": false}, {"id": "edge-redirect-2-local-cache", "source": "redirect-service-2", "target": "local-cache-1", "type": "custom", "label": "Local Cache", "data": {"connectionNumber": "Local Cache Access"}, "style": {"stroke": "#ea580c", "strokeWidth": 2}, "markerEnd": {"type": "arrowclosed", "color": "#ea580c"}, "selected": false}, {"id": "edge-redirect-2-cache-2", "source": "redirect-service-2", "target": "redis-cluster-2", "type": "custom", "label": "Secondary Cache", "data": {"connectionNumber": "Secondary C<PERSON>up"}, "style": {"stroke": "#dc2626", "strokeWidth": 2}, "markerEnd": {"type": "arrowclosed", "color": "#dc2626"}, "selected": false}, {"id": "edge-cache-replication", "source": "redis-cluster-1", "target": "redis-cluster-2", "type": "custom", "label": "Cross-Cluster Replication", "data": {"connectionNumber": "Cache Replication"}, "style": {"stroke": "#dc2626", "strokeWidth": 2, "strokeDasharray": "5,5"}, "markerEnd": {"type": "arrowclosed", "color": "#dc2626"}, "selected": false}, {"id": "edge-cdn-dns", "source": "cdn-global-1", "target": "dns-failover-1", "type": "custom", "label": "DNS Resolution", "data": {"connectionNumber": "DNS Resolution"}, "style": {"stroke": "#0891b2", "strokeWidth": 2}, "markerEnd": {"type": "arrowclosed", "color": "#0891b2"}, "selected": false}, {"id": "edge-dns-lb-1", "source": "dns-failover-1", "target": "load-balancer-global-1", "type": "custom", "label": "Primary Route", "data": {"connectionNumber": "Primary DNS Route"}, "style": {"stroke": "#0891b2", "strokeWidth": 2}, "markerEnd": {"type": "arrowclosed", "color": "#0891b2"}, "selected": false}, {"id": "edge-dns-lb-2", "source": "dns-failover-1", "target": "load-balancer-global-2", "type": "custom", "label": "Failover Route", "data": {"connectionNumber": "Failover DNS Route"}, "style": {"stroke": "#0891b2", "strokeWidth": 2, "strokeDasharray": "5,5"}, "markerEnd": {"type": "arrowclosed", "color": "#0891b2"}, "selected": false}, {"id": "edge-lb-2-gateway-1", "source": "load-balancer-global-2", "target": "api-gateway-1", "type": "custom", "label": "Load Balanced Requests", "data": {"connectionNumber": "Load Balanced Requests (LB2)"}, "style": {"stroke": "#222", "strokeWidth": 2}, "markerEnd": {"type": "arrowclosed", "color": "#222"}, "selected": false}, {"id": "edge-lb-2-gateway-2", "source": "load-balancer-global-2", "target": "api-gateway-2", "type": "custom", "label": "Load Balanced Requests", "data": {"connectionNumber": "Load Balanced Requests (LB2)"}, "style": {"stroke": "#222", "strokeWidth": 2}, "markerEnd": {"type": "arrowclosed", "color": "#222"}, "selected": false}, {"id": "edge-kafka-schema-registry", "source": "kafka-stream-1", "target": "schema-registry-1", "type": "custom", "label": "Schema Management", "data": {"connectionNumber": "Schema Management"}, "style": {"stroke": "#8b5cf6", "strokeWidth": 2}, "markerEnd": {"type": "arrowclosed", "color": "#8b5cf6"}, "selected": false}, {"id": "edge-kafka-consumer-state", "source": "kafka-stream-1", "target": "consumer-state-manager-1", "type": "custom", "label": "Consumer Management", "data": {"connectionNumber": "Consumer State Management"}, "style": {"stroke": "#14b8a6", "strokeWidth": 2}, "markerEnd": {"type": "arrowclosed", "color": "#14b8a6"}, "selected": false}, {"id": "edge-analytics-consumer-state", "source": "analytics-service-1", "target": "consumer-state-manager-1", "type": "custom", "label": "Consumer Coordination", "data": {"connectionNumber": "Consumer Coordination"}, "style": {"stroke": "#14b8a6", "strokeWidth": 2}, "markerEnd": {"type": "arrowclosed", "color": "#14b8a6"}, "selected": false}, {"id": "edge-schema-registry-monitoring", "source": "schema-registry-1", "target": "monitoring-system-1", "type": "custom", "label": "<PERSON><PERSON><PERSON> Met<PERSON>s", "data": {"connectionNumber": "Schema Registry Metrics"}, "style": {"stroke": "#222", "strokeWidth": 2}, "markerEnd": {"type": "arrowclosed", "color": "#222"}, "selected": false}, {"id": "edge-consumer-state-monitoring", "source": "consumer-state-manager-1", "target": "monitoring-system-1", "type": "custom", "label": "Consumer Metrics", "data": {"connectionNumber": "Consumer State Metrics"}, "style": {"stroke": "#222", "strokeWidth": 2}, "markerEnd": {"type": "arrowclosed", "color": "#222"}, "selected": false}]}