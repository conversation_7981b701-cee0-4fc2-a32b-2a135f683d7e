import { cn } from "@/lib/utils";
import {
  IconBrandFacebook,
  IconBrandGithub,
  IconBrandInstagram,
  IconBrandLinkedin,
  IconBrandTwitter,
} from "@tabler/icons-react";
import React from "react";
import { motion } from "framer-motion";

export function FooterWithGrid() {
  const pages = [
    {
      title: "Privacy",
      href: "#",
    },
    {
      title: "Terms",
      href: "#",
    },
  ];

  return (
    <div className="border-t border-slate-200 px-8 py-20 bg-slate-50 w-full relative overflow-hidden">
      <div className="max-w-7xl mx-auto text-sm text-slate-600 justify-between items-start md:px-8">
        <div className="flex flex-col items-center justify-center w-full relative">
          <motion.div 
            className="mr-0 md:mr-4 md:flex mb-4"
            whileHover={{ scale: 1.05 }}
            transition={{ type: "spring", stiffness: 400, damping: 10 }}
          >
            <Logo />
          </motion.div>

          <ul className="transition-colors flex sm:flex-row flex-col text-slate-600 hover:text-slate-800 list-none gap-6 mb-8">
            {pages.map((page, idx) => (
              <motion.li 
                key={"pages" + idx} 
                className="list-none"
                whileHover={{ y: -1 }}
                transition={{ type: "spring", stiffness: 400, damping: 10 }}
              >
                <a
                  className="transition-colors hover:text-indigo-600 font-inter font-medium"
                  href={page.href}
                >
                  {page.title}
                </a>
              </motion.li>
            ))}
          </ul>

          <GridLineHorizontal className="max-w-7xl mx-auto mt-8" />
        </div>
        <div className="flex sm:flex-row flex-col justify-between mt-8 items-center w-full">
          <p className="text-slate-500 font-inter mb-8 sm:mb-0">
            &copy; 2025 Layrs. All rights reserved.
          </p>
          <div className="flex gap-4">
            <SocialLink href="https://www.linkedin.com/company/layrs" icon={IconBrandLinkedin} />
            <SocialLink href="https://www.instagram.com/layrs.me?igsh=MXZmdWlydHNjejd0aw==" icon={IconBrandInstagram} />
          </div>
        </div>
      </div>
    </div>
  );
}

const SocialLink = ({ href, icon: Icon }: { href: string; icon: React.ComponentType<{ className?: string }> }) => (
  <motion.div
    whileHover={{ scale: 1.1, y: -1 }}
    transition={{ type: "spring", stiffness: 400, damping: 10 }}
  >
    <a href={href} className="text-slate-400 hover:text-indigo-600 transition-colors">
      <Icon className="h-5 w-5" />
    </a>
  </motion.div>
);

const GridLineHorizontal = ({
  className,
  offset,
}: {
  className?: string;
  offset?: string;
}) => {
  return (
    <div
      style={
        {
          "--background": "#f8fafc",
          "--color": "rgba(71, 85, 105, 0.2)",
          "--height": "1px",
          "--width": "5px",
          "--fade-stop": "90%",
          "--offset": offset || "200px",
          "--color-dark": "rgba(148, 163, 184, 0.2)",
          maskComposite: "exclude",
        } as React.CSSProperties
      }
      className={cn(
        "w-[calc(100%+var(--offset))] h-[var(--height)]",
        "bg-[linear-gradient(to_right,var(--color),var(--color)_50%,transparent_0,transparent)]",
        "[background-size:var(--width)_var(--height)]",
        "[mask:linear-gradient(to_left,var(--background)_var(--fade-stop),transparent),_linear-gradient(to_right,var(--background)_var(--fade-stop),transparent),_linear-gradient(black,black)]",
        "[mask-composite:exclude]",
        "z-30",
        className
      )}
    ></div>
  );
};

const Logo = () => {
  return (
    <a
      href="/"
      className="font-normal flex space-x-2 items-center text-sm mr-4 text-slate-900 px-2 py-1 relative z-20"
    >
      <img
        src="/Layrs.jpeg"
        alt="Layrs Logo"
        className="h-8 w-8"
      />
      <span className="font-bold text-xl text-slate-900 font-inter">Layrs</span>
    </a>
  );
};