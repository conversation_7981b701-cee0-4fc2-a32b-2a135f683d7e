import React from 'react';
import { Question } from '@/types/Question';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

import { CheckCircle, AlertCircle, Ban, Home } from 'lucide-react';
import { <PERSON>roll<PERSON><PERSON> } from '@/components/ui/scroll-area';

interface ProblemContextProps {
  question: Question | null;
}

const difficultyColors = {
  easy: 'bg-green-100 text-green-800 border-green-200',
  medium: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  hard: 'bg-red-100 text-red-800 border-red-200'
};

const categoryColors = {
  'system-design': 'bg-blue-100 text-blue-800 border-blue-200',
  'architecture': 'bg-purple-100 text-purple-800 border-purple-200',
  'scalability': 'bg-orange-100 text-orange-800 border-orange-200',
  'database': 'bg-cyan-100 text-cyan-800 border-cyan-200',
  'frontend': 'bg-pink-100 text-pink-800 border-pink-200',
  'backend': 'bg-indigo-100 text-indigo-800 border-indigo-200'
};

// Helper function to get FR and NFR from question data
const getRequirements = (question: Question) => {
  // Use explicit FR/NFR columns if available, otherwise fall back to categorization
  if (question.functional_requirements && question.functional_requirements.length > 0 &&
      question.non_functional_requirements && question.non_functional_requirements.length > 0) {
    return {
      functionalReqs: question.functional_requirements,
      nonFunctionalReqs: question.non_functional_requirements
    };
  }

  // Fallback to legacy categorization for backward compatibility
  return categorizeRequirementsLegacy(question.requirements);
};

// Legacy helper function for backward compatibility
const categorizeRequirementsLegacy = (requirements: string[]) => {
  const functionalReqs: string[] = [];
  const nonFunctionalReqs: string[] = [];

  const nfrKeywords = [
    'scalability', 'performance', 'security', 'reliability',
    'availability', 'maintainability', 'usability', 'efficiency',
    'fast', 'secure', 'reliable', 'available', 'maintainable', 'usable',
    'efficient', 'scale', 'latency', 'throughput', 'response time',
    'concurrent', 'simultaneously', 'load', 'traffic', 'volume'
  ];

  requirements.forEach(req => {
    const lowerReq = req.toLowerCase();
    if (nfrKeywords.some(keyword => lowerReq.includes(keyword))) {
      nonFunctionalReqs.push(req);
    } else {
      functionalReqs.push(req);
    }
  });

  return { functionalReqs, nonFunctionalReqs };
};

const ProblemContext: React.FC<ProblemContextProps> = ({ question }) => {
  if (!question) {
    return (
      <div className="p-4 text-center text-gray-500">
        <div className="flex flex-col items-center gap-3">
          <Home className="h-8 w-8 text-green-600" />
          <div>
            <p className="font-medium text-gray-700">Free Canvas Mode</p>
            <p className="text-sm">Design freely without constraints. Go to Questions to select a specific problem.</p>
          </div>
        </div>
      </div>
    );
  }

  const { functionalReqs, nonFunctionalReqs } = getRequirements(question);

  return (
    <ScrollArea className="h-full pr-4">
      <div className="space-y-6 pb-4">
        {/* Problem Section */}
        <Card className="border-blue-200">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg text-layrs-dark">Problem</CardTitle>
              <div className="flex items-center gap-2">
                <Badge className={difficultyColors[question.difficulty]}>
                  {question.difficulty.charAt(0).toUpperCase() + question.difficulty.slice(1)}
                </Badge>
                <Badge className={categoryColors[question.category]}>
                  {question.category.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
                </Badge>
              </div>
            </div>
            <CardDescription className="text-base font-medium text-layrs-dark">
              {question.title}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-700">{question.description}</p>

            <div className="flex flex-wrap gap-1 mt-3">
              {question.tags
                .filter(tag => tag.toLowerCase() !== 'system design'.toLowerCase())
                .map((tag) => (
                  <Badge key={tag} variant="outline" className="text-xs">
                    {tag}
                  </Badge>
                ))}
            </div>
          </CardContent>
        </Card>

        {/* Functional Requirements Section */}
        <Card className="border-green-200">
          <CardHeader className="pb-2">
            <CardTitle className="text-md flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-600" />
              Functional Requirements (FR)
            </CardTitle>
            <CardDescription>
              What the system should do
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2 pl-6 list-disc text-sm">
              {functionalReqs.map((req, index) => (
                <li key={`fr-${index}`} className="text-gray-700">{req}</li>
              ))}
            </ul>
          </CardContent>
        </Card>

        {/* Non-Functional Requirements Section */}
        {nonFunctionalReqs.length > 0 && (
          <Card className="border-blue-200">
            <CardHeader className="pb-2">
              <CardTitle className="text-md flex items-center gap-2">
                <AlertCircle className="h-4 w-4 text-blue-600" />
                Non-Functional Requirements (NFR)
              </CardTitle>
              <CardDescription>
                How the system should perform
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 pl-6 list-disc text-sm">
                {nonFunctionalReqs.map((req, index) => (
                  <li key={`nfr-${index}`} className="text-gray-700">{req}</li>
                ))}
              </ul>
            </CardContent>
          </Card>
        )}

        {/* Constraints Section */}
        {question.constraints && question.constraints.length > 0 && (
          <Card className="border-red-200">
            <CardHeader className="pb-2">
              <CardTitle className="text-md flex items-center gap-2">
                <Ban className="h-4 w-4 text-red-600" />
                Additional Context
              </CardTitle>
              <CardDescription>
                hints and suggestions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 pl-6 list-disc text-sm">
                {question.constraints.map((constraint, index) => (
                  <li key={`constraint-${index}`} className="text-gray-700">{constraint}</li>
                ))}
              </ul>
            </CardContent>
          </Card>
        )}
      </div>
    </ScrollArea>
  );
};

export default ProblemContext;
