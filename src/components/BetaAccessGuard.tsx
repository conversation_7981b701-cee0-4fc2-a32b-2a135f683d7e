import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useBetaAccess } from '@/hooks/useBetaAccess';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Loader2, Lock, Mail } from 'lucide-react';

interface BetaAccessGuardProps {
  children: React.ReactNode;
}

const BetaAccessGuard: React.FC<BetaAccessGuardProps> = ({ children }) => {
  const { user, loading: authLoading } = useAuth();
  const { hasBetaAccess, loading: betaLoading } = useBetaAccess();
  const location = useLocation();

  // Show loading while checking authentication and beta access
  if (authLoading || betaLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-[#1e1b4b] via-[#2e1065] to-[#1e1b4b]">
        <Card className="w-full max-w-md">
          <CardContent className="flex items-center justify-center py-12">
            <Loader2 className="w-8 h-8 animate-spin text-blue-500" />
            <span className="ml-2">Checking access...</span>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Redirect to login if not authenticated
  if (!user) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // Show beta access required message if user doesn't have beta access
  if (!hasBetaAccess) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-[#1e1b4b] via-[#2e1065] to-[#1e1b4b]">
        <Card className="w-full max-w-lg">
          <CardHeader className="text-center">
            <Lock className="w-16 h-16 text-yellow-500 mx-auto mb-4" />
            <CardTitle className="text-yellow-600">Beta Access Required</CardTitle>
            <CardDescription className="text-lg">
              This feature is currently in beta and requires special access.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="font-semibold text-blue-900 mb-2">How to get beta access:</h3>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• Join our waitlist on the homepage</li>
                <li>• Wait for a beta invitation email</li>
                <li>• Accept your invitation when received</li>
                <li>• Get early access to new features</li>
              </ul>
            </div>

            <div className="bg-yellow-50 p-4 rounded-lg">
              <div className="flex items-start">
                <Mail className="w-5 h-5 text-yellow-600 mt-0.5 mr-2 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-yellow-800">Already invited?</h4>
                  <p className="text-sm text-yellow-700 mt-1">
                    Check your email for a beta invitation link. If you can't find it, 
                    check your spam folder or contact support.
                  </p>
                </div>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-3">
              <Button 
                onClick={() => window.location.href = '/'} 
                className="flex-1 bg-blue-600 hover:bg-blue-700"
              >
                Join Waitlist
              </Button>
              <Button 
                onClick={() => window.location.href = '/login'} 
                variant="outline"
                className="flex-1"
              >
                Back to Login
              </Button>
            </div>

            <div className="text-center">
              <p className="text-sm text-gray-500">
                Signed in as: <span className="font-medium">{user.email}</span>
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // User has beta access, render the protected content
  return <>{children}</>;
};

export default BetaAccessGuard;
