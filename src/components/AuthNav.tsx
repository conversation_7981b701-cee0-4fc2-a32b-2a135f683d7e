import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useBetaAccess } from '@/hooks/useBetaAccess';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { LogOut, User, Star } from 'lucide-react';

const AuthNav: React.FC = () => {
  const { user, signOut } = useAuth();
  const { hasBetaAccess } = useBetaAccess();
  const navigate = useNavigate();

  const handleSignOut = async () => {
    await signOut();
    navigate('/login');
  };

  const getInitials = (email: string) => {
    return email.substring(0, 2).toUpperCase();
  };

  return (
    <div className="flex items-center gap-4">
      {user ? (
        <div className="flex items-center gap-2">
          {hasBetaAccess && (
            <Badge className="bg-gradient-to-r from-purple-500 to-blue-500 text-white border-0 flex items-center gap-1">
              <Star className="h-3 w-3" />
              Beta
            </Badge>
          )}
          <div className="flex items-center gap-2">
            {/* Profile Button */}
            <button
              onClick={() => {
                console.log('Profile button clicked - navigating to /profile');
                navigate('/profile');
              }}
              className="relative h-8 w-8 rounded-full hover:bg-white/10 focus:bg-white/10 transition-colors cursor-pointer"
              title="Go to Profile"
            >
              <div className="h-8 w-8 rounded-full bg-purple-600 text-white flex items-center justify-center text-sm font-medium">
                {getInitials(user.email || 'User')}
              </div>
            </button>

            {/* Sign Out Button */}
            <button
              onClick={() => {
                console.log('Sign out button clicked');
                handleSignOut();
              }}
              className="p-1 rounded hover:bg-white/10 transition-colors cursor-pointer text-red-400 hover:text-red-300"
              title="Sign Out"
            >
              <LogOut className="h-4 w-4" />
            </button>
          </div>
        </div>
      ) : (
        <div className="flex gap-2">
          <Button variant="outline" asChild>
            <Link to="/login">Log in</Link>
          </Button>
          <Button asChild>
            <Link to="/register">Sign up</Link>
          </Button>
        </div>
      )}
    </div>
  );
};

export default AuthNav;
