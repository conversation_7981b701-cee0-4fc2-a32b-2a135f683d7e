import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { QuestionService } from '@/services/questionService';
import { useQuestions } from '@/contexts/QuestionsContext';
import { Database, RefreshCw, CheckCircle, XCircle } from 'lucide-react';

const QuestionLoadTest: React.FC = () => {
  const { questions, loading, error } = useQuestions();
  const [directLoadTest, setDirectLoadTest] = useState<{
    status: 'idle' | 'loading' | 'success' | 'error';
    questions: any[];
    error?: string;
  }>({
    status: 'idle',
    questions: [],
  });

  const [connectionTest, setConnectionTest] = useState<{
    status: 'idle' | 'loading' | 'success' | 'error';
    result?: any;
  }>({
    status: 'idle',
  });

  const testDirectLoad = async () => {
    setDirectLoadTest({ status: 'loading', questions: [] });
    
    try {
      console.log('🧪 Testing direct question loading...');
      const loadedQuestions = await QuestionService.loadQuestions();
      
      setDirectLoadTest({
        status: 'success',
        questions: loadedQuestions,
      });
      
      console.log('✅ Direct load successful:', loadedQuestions);
    } catch (error) {
      console.error('❌ Direct load failed:', error);
      setDirectLoadTest({
        status: 'error',
        questions: [],
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  };

  const testConnection = async () => {
    setConnectionTest({ status: 'loading' });
    
    try {
      console.log('🧪 Testing connection status...');
      const status = await QuestionService.getConnectionStatus();
      
      setConnectionTest({
        status: 'success',
        result: status,
      });
      
      console.log('✅ Connection test successful:', status);
    } catch (error) {
      console.error('❌ Connection test failed:', error);
      setConnectionTest({
        status: 'error',
        result: { error: error instanceof Error ? error.message : 'Unknown error' },
      });
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'loading':
        return <RefreshCw className="h-4 w-4 animate-spin text-blue-500" />;
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Database className="h-4 w-4 text-gray-400" />;
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Question Loading Test
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Context Loading Status */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-medium mb-3">Questions Context Status</h4>
            <div className="grid grid-cols-3 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Loading:</span>
                <span className="ml-2 font-medium">{loading ? 'Yes' : 'No'}</span>
              </div>
              <div>
                <span className="text-gray-600">Questions Count:</span>
                <span className="ml-2 font-medium">{questions.length}</span>
              </div>
              <div>
                <span className="text-gray-600">Error:</span>
                <span className="ml-2 font-medium">{error || 'None'}</span>
              </div>
            </div>
            
            {questions.length > 0 && (
              <div className="mt-3">
                <h5 className="font-medium text-sm mb-2">Sample Questions:</h5>
                <div className="space-y-1">
                  {questions.slice(0, 3).map(q => (
                    <div key={q.id} className="text-xs bg-white p-2 rounded border">
                      <span className="font-medium">{q.title}</span>
                      <span className="ml-2 text-gray-500">({q.difficulty})</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Test Buttons */}
          <div className="flex gap-3">
            <Button onClick={testDirectLoad} disabled={directLoadTest.status === 'loading'}>
              {getStatusIcon(directLoadTest.status)}
              <span className="ml-2">Test Direct Load</span>
            </Button>
            
            <Button onClick={testConnection} disabled={connectionTest.status === 'loading'} variant="outline">
              {getStatusIcon(connectionTest.status)}
              <span className="ml-2">Test Connection</span>
            </Button>
          </div>

          {/* Direct Load Results */}
          {directLoadTest.status !== 'idle' && (
            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="font-medium mb-2 flex items-center gap-2">
                {getStatusIcon(directLoadTest.status)}
                Direct Load Test Results
              </h4>
              
              {directLoadTest.status === 'success' && (
                <div>
                  <p className="text-sm text-green-700 mb-2">
                    ✅ Successfully loaded {directLoadTest.questions.length} questions
                  </p>
                  <div className="text-xs space-y-1">
                    {directLoadTest.questions.slice(0, 2).map(q => (
                      <div key={q.id} className="bg-white p-2 rounded">
                        <strong>{q.title}</strong> - {q.difficulty}
                      </div>
                    ))}
                  </div>
                </div>
              )}
              
              {directLoadTest.status === 'error' && (
                <p className="text-sm text-red-700">
                  ❌ Error: {directLoadTest.error}
                </p>
              )}
            </div>
          )}

          {/* Connection Results */}
          {connectionTest.status !== 'idle' && (
            <div className="bg-green-50 p-4 rounded-lg">
              <h4 className="font-medium mb-2 flex items-center gap-2">
                {getStatusIcon(connectionTest.status)}
                Connection Test Results
              </h4>
              
              {connectionTest.result && (
                <pre className="text-xs bg-white p-2 rounded overflow-auto">
                  {JSON.stringify(connectionTest.result, null, 2)}
                </pre>
              )}
            </div>
          )}

          {/* Data Source Indicator */}
          <div className="bg-yellow-50 p-4 rounded-lg">
            <h4 className="font-medium mb-2">Data Source Analysis</h4>
            <div className="text-sm space-y-1">
              <div>
                <strong>Questions Context:</strong> {questions.length > 0 ? 
                  `Loaded ${questions.length} questions` : 
                  'No questions loaded'
                }
              </div>
              <div>
                <strong>Source Detection:</strong> {
                  questions.length === 5 && questions[0]?.id === '1' ? 
                    'Likely from database (5 questions with ID "1")' :
                    questions.length > 0 ? 
                      'Unknown source' : 
                      'No data'
                }
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default QuestionLoadTest;
