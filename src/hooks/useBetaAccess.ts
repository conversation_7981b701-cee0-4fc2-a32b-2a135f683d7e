import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { checkBetaAccess } from '@/services/betaService';
import { debugLog, debugWarn, debugError } from '@/utils/debugLogger';

// Global cache for beta access status
const betaAccessCache = new Map<string, { hasAccess: boolean; timestamp: number }>();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

export const useBetaAccess = () => {
  const [hasBetaAccess, setHasBetaAccess] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);
  const { user } = useAuth();

  useEffect(() => {
    const checkAccess = async () => {
      if (!user?.email) {
        setHasBetaAccess(false);
        setLoading(false);
        return;
      }

      const email = user.email.toLowerCase().trim();
      const now = Date.now();

      // Check cache first
      const cached = betaAccessCache.get(email);
      if (cached && (now - cached.timestamp) < CACHE_DURATION) {
        debugLog('Using cached beta access status for', email);
        setHasBetaAccess(cached.hasAccess);
        setLoading(false);
        return;
      }

      try {
        debugLog('Checking beta access for', email);
        const hasAccess = await checkBetaAccess(email);

        // Cache the result
        betaAccessCache.set(email, { hasAccess, timestamp: now });

        setHasBetaAccess(hasAccess);
      } catch (error) {
        console.error('Error checking beta access:', error);
        setHasBetaAccess(false);
      } finally {
        setLoading(false);
      }
    };

    checkAccess();
  }, [user?.email]);

  return { hasBetaAccess, loading };
};

// Helper function to clear cache (useful for testing or when beta status changes)
export const clearBetaAccessCache = (email?: string) => {
  if (email) {
    betaAccessCache.delete(email.toLowerCase().trim());
  } else {
    betaAccessCache.clear();
  }
};
