import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { subscriptionService, Subscription, PlanInfo, UserData } from '@/services/subscriptionService';
import { debugLog, debugWarn, debugError } from '@/utils/debugLogger';

export interface UseSubscriptionReturn {
  subscription: Subscription | null;
  planInfo: PlanInfo | null;
  credits: number;
  loading: boolean;
  error: string | null;
  isProUser: boolean;
  canUpgrade: boolean;
  hasActiveSubscription: boolean;
  canAccessQuestion: (questionId: number) => Promise<boolean>;
  getAccessibleQuestionIds: () => Promise<number[]>;
  refetch: () => Promise<void>;
}

export const useSubscription = (): UseSubscriptionReturn => {
  const { user } = useAuth();
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [planInfo, setPlanInfo] = useState<PlanInfo | null>(null);
  const [credits, setCredits] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const fetchUserData = useCallback(async () => {
    if (!user) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      debugLog('Fetching user subscription data...');
      const userData: UserData = await subscriptionService.getUserData(user.id);
      
      setSubscription(userData.subscription);
      setPlanInfo(userData.planInfo);
      setCredits(userData.credits);
      
      debugLog('User subscription data loaded:', userData);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load subscription data';
      debugError('Error fetching subscription data:', err);
      setError(errorMessage);
      
      // Set default values for basic users on error
      setSubscription(null);
      setPlanInfo({
        id: 'basic',
        name: 'Basic Plan',
        price: 0,
        credits: 10,
        period: 'monthly'
      });
      setCredits(10);
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Load subscription data when user changes
  useEffect(() => {
    fetchUserData();
  }, [fetchUserData]);

  // Derived values
  const isProUser = subscription?.status === 'active';
  const canUpgrade = !isProUser;
  const hasActiveSubscription = isProUser;

  // Function to check if user can access a specific question
  const canAccessQuestion = useCallback(async (questionId: number): Promise<boolean> => {
    if (!user) return false;
    return await subscriptionService.canAccessQuestion(questionId, user.id);
  }, [user]);

  // Function to get accessible question IDs
  const getAccessibleQuestionIds = useCallback(async (): Promise<number[]> => {
    if (!user) return [1]; // Default to basic access
    return await subscriptionService.getAccessibleQuestionIds(user.id);
  }, [user]);

  // Refetch function to manually refresh subscription data
  const refetch = useCallback(async () => {
    await fetchUserData();
  }, [fetchUserData]);

  return {
    subscription,
    planInfo,
    credits,
    loading,
    error,
    isProUser,
    canUpgrade,
    hasActiveSubscription,
    canAccessQuestion,
    getAccessibleQuestionIds,
    refetch
  };
};
